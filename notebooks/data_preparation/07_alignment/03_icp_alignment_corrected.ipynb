logger.info("Starting model training...")
start_time = time.time()

history = model.fit(
    X_train_split,
    y_train_split,
    batch_size=batch_size,
    epochs=epochs,
    validation_data=(X_val, y_val),
    verbose=1,
    callbacks=callbacks
)

end_time = time.time()
training_time = round(end_time - start_time, 2)

logger.info(f"Training completed in {training_time} seconds")

# Save final model
model_path = os.path.join(output_dir, "pointnet_alignment_model.h5")
model.save(model_path)
logger.info(f"Saved trained model to {model_path}")

# Parameters (Papermill)
ground_method = "csf"  # Ground segmentation method: csf, pmf, ransac
site_name = "trino_enel"
icp_max_iterations = 50
icp_tolerance = 1e-6
voxel_size = 0.02  # For downsampling if needed
output_dir = "../../../data/output_runs/icp_alignment_corrected"
enable_visualization = True
save_results = True
use_coordinate_correction = True  # Enable coordinate correction

# Imports
import numpy as np
import open3d as o3d
import matplotlib.pyplot as plt
import pandas as pd
import time
import json
from pathlib import Path
from datetime import datetime
from scipy.spatial import cKDTree

# Setup
np.random.seed(42)
output_path = Path(output_dir) / ground_method
output_path.mkdir(parents=True, exist_ok=True)

print(f"ICP ALIGNMENT WITH COORDINATE CORRECTION - {ground_method.upper()}")
print(f"Site: {site_name}")
print(f"Output: {output_path}")
print(f"Coordinate correction: {'Enabled' if use_coordinate_correction else '❌ Disabled'}")
print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

!ls -lh ../../../data/processed/trino_enel

# Define file paths - Use corrected coordinates if available
if use_coordinate_correction:
    corrected_drone_file = Path(f"../../../data/processed/{site_name}/aligned_coordinates/{site_name}_drone_{ground_method}_corrected.ply")
    corrected_ifc_file = Path(f"../../../data/processed/{site_name}/aligned_coordinates/{site_name}_ifc_corrected.ply")
    
    # Use corrected files if they exist, otherwise fall back to original
    if corrected_drone_file.exists() and corrected_ifc_file.exists():
        drone_file = corrected_drone_file
        ifc_file = corrected_ifc_file
        coordinate_status = "Using coordinate-corrected point clouds"
        print(coordinate_status)
        print(f"  Drone (corrected): {drone_file}")
        print(f"  IFC (corrected): {ifc_file}")
    else:
        drone_file = Path(f"../../data/processed/{site_name}/ground_segmentation/{ground_method}/trino_enel_nonground.ply")
        ifc_file = Path(f"../../data/processed/{site_name}/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply")
        coordinate_status = "Corrected files not found - using original point clouds"
        print(coordinate_status)
        print(f"  Run 01_coordinate_correction.ipynb first for better results")
        print(f"  Drone (original): {drone_file}")
        print(f"  IFC (original): {ifc_file}")
else:
    # Use original files
    drone_file = Path(f"../../../data/processed/{site_name}/ground_segmentation/{ground_method}/trino_enel_nonground.ply")
    ifc_file = Path(f"../../../data/processed/{site_name}/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply")
    coordinate_status = "Using original point clouds (coordinate correction disabled)"
    print(coordinate_status)

print(f"\nLoading point clouds...")
print(f"Drone exists: {drone_file.exists()}")
print(f"IFC exists: {ifc_file.exists()}")

# Load point clouds
if drone_file.exists() and ifc_file.exists():
    drone_pcd = o3d.io.read_point_cloud(str(drone_file))
    ifc_pcd = o3d.io.read_point_cloud(str(ifc_file))
    
    drone_points = np.asarray(drone_pcd.points)
    ifc_points = np.asarray(ifc_pcd.points)
    
    print(f"Loaded drone scan: {drone_points.shape[0]:,} points")
    print(f"Loaded IFC model: {ifc_points.shape[0]:,} points")
    
    # Store original for comparison
    drone_pcd_original = drone_pcd
    ifc_pcd_original = ifc_pcd
    
else:
    print("Error: Required point cloud files not found!")
    if not drone_file.exists():
        print(f"  Missing: {drone_file}")
    if not ifc_file.exists():
        print(f"  Missing: {ifc_file}")
    raise FileNotFoundError("Missing point cloud files")

# Display initial statistics and coordinate analysis
print("\nPOINT CLOUD STATISTICS")
print("\nDrone scan (ground truth):")
print(f"  Points: {drone_points.shape[0]:,}")
print(f"  X range: [{drone_points[:, 0].min():.2f}, {drone_points[:, 0].max():.2f}]")
print(f"  Y range: [{drone_points[:, 1].min():.2f}, {drone_points[:, 1].max():.2f}]")
print(f"  Z range: [{drone_points[:, 2].min():.2f}, {drone_points[:, 2].max():.2f}]")

print("\nIFC model (to be aligned):")
print(f"  Points: {ifc_points.shape[0]:,}")
print(f"  X range: [{ifc_points[:, 0].min():.2f}, {ifc_points[:, 0].max():.2f}]")
print(f"  Y range: [{ifc_points[:, 1].min():.2f}, {ifc_points[:, 1].max():.2f}]")
print(f"  Z range: [{ifc_points[:, 2].min():.2f}, {ifc_points[:, 2].max():.2f}]")

# Calculate initial offset
drone_center = drone_points.mean(axis=0)
ifc_center = ifc_points.mean(axis=0)
initial_offset = drone_center - ifc_center

print(f"\nINITIAL COORDINATE ANALYSIS")
print(f"Drone center: [{drone_center[0]:.2f}, {drone_center[1]:.2f}, {drone_center[2]:.2f}]")
print(f"IFC center:   [{ifc_center[0]:.2f}, {ifc_center[1]:.2f}, {ifc_center[2]:.2f}]")
print(f"Initial offset: [{initial_offset[0]:.2f}, {initial_offset[1]:.2f}, {initial_offset[2]:.2f}]")
print(f"Offset magnitude: {np.linalg.norm(initial_offset):.2f} meters")

# Assess coordinate compatibility
offset_magnitude = np.linalg.norm(initial_offset)
if offset_magnitude < 10:
    print(f"Good coordinate alignment (offset: {offset_magnitude:.2f}m)")
elif offset_magnitude < 100:
    print(f"Moderate coordinate offset (offset: {offset_magnitude:.2f}m)")
else:
    print(f"Large coordinate offset (offset: {offset_magnitude:.2f}m)")
    print(f"   Consider running coordinate correction first")

# Convert to Open3D point clouds
print("\nPREPARING POINT CLOUDS FOR ICP")

# Create Open3D point clouds
source_pcd = o3d.geometry.PointCloud()
source_pcd.points = o3d.utility.Vector3dVector(ifc_points)

target_pcd = o3d.geometry.PointCloud()
target_pcd.points = o3d.utility.Vector3dVector(drone_points)

print(f"Source (IFC): {len(source_pcd.points):,} points")
print(f"Target (Drone): {len(target_pcd.points):,} points")

# Downsample if needed
if voxel_size > 0:
    print(f"\nDOWNSAMPLING (voxel size: {voxel_size}m)")
    source_pcd = source_pcd.voxel_down_sample(voxel_size)
    target_pcd = target_pcd.voxel_down_sample(voxel_size)
    print(f"After downsampling - Source: {len(source_pcd.points):,}, Target: {len(target_pcd.points):,}")

# Run ICP alignment
print(f"\nRUNNING ICP ALIGNMENT")
print(f"Max iterations: {icp_max_iterations}")
print(f"Tolerance: {icp_tolerance}")

# Initial transformation (identity)
init_transformation = np.eye(4)

# Run ICP
import time
start_time = time.time()

icp_result = o3d.pipelines.registration.registration_icp(
    source_pcd, target_pcd,
    max_correspondence_distance=2.0,
    init=init_transformation,
    estimation_method=o3d.pipelines.registration.TransformationEstimationPointToPoint(),
    criteria=o3d.pipelines.registration.ICPConvergenceCriteria(
        max_iteration=icp_max_iterations,
        relative_fitness=icp_tolerance,
        relative_rmse=icp_tolerance
    )
)

icp_time = time.time() - start_time

print(f"\nICP COMPLETED in {icp_time:.2f} seconds")
print(f"Fitness: {icp_result.fitness:.6f}")
print(f"Inlier RMSE: {icp_result.inlier_rmse:.6f}")
print(f"Correspondence set size: {len(icp_result.correspondence_set)}")

# Apply transformation and calculate metrics
print(f"\nALIGNMENT RESULTS")

# Apply transformation to original IFC points
ifc_points_homogeneous = np.hstack([ifc_points, np.ones((ifc_points.shape[0], 1))])
aligned_ifc_points = (icp_result.transformation @ ifc_points_homogeneous.T).T[:, :3]

# Calculate RMSE using nearest neighbor distances
from scipy.spatial import cKDTree
tree = cKDTree(drone_points)
distances, _ = tree.query(aligned_ifc_points)

rmse = np.sqrt(np.mean(distances**2))
mean_distance = np.mean(distances)
max_distance = np.max(distances)

print(f"RMSE: {rmse:.3f} m")
print(f"Mean distance: {mean_distance:.3f} m")
print(f"Max distance: {max_distance:.3f} m")
print(f"ICP Fitness: {icp_result.fitness:.6f}")
print(f"ICP Inlier RMSE: {icp_result.inlier_rmse:.6f} m")

# Transformation matrix
print(f"\nTRANSFORMATION MATRIX:")
print(icp_result.transformation)

# Save results
if save_results:
    print(f"\nSAVING RESULTS")
    
    # Save aligned point cloud
    aligned_pcd = o3d.geometry.PointCloud()
    aligned_pcd.points = o3d.utility.Vector3dVector(aligned_ifc_points)
    aligned_file = output_path / f"aligned_ifc_{ground_method}.ply"
    o3d.io.write_point_cloud(str(aligned_file), aligned_pcd)
    print(f"Aligned point cloud: {aligned_file}")
    
    # Save transformation matrix
    transform_file = output_path / f"transformation_matrix_{ground_method}.npy"
    np.save(transform_file, icp_result.transformation)
    print(f"Transformation matrix: {transform_file}")
    
    # Save metrics
    import json
    metrics = {
        'ground_method': ground_method,
        'site_name': site_name,
        'rmse': float(rmse),
        'mean_distance': float(mean_distance),
        'max_distance': float(max_distance),
        'icp_fitness': float(icp_result.fitness),
        'icp_inlier_rmse': float(icp_result.inlier_rmse),
        'icp_time': float(icp_time),
        'correspondence_count': len(icp_result.correspondence_set),
        'source_points': len(source_pcd.points),
        'target_points': len(target_pcd.points),
        'voxel_size': voxel_size,
        'max_iterations': icp_max_iterations,
        'tolerance': icp_tolerance,
        'coordinate_correction_used': use_coordinate_correction,
        'timestamp': datetime.now().isoformat()
    }
    
    metrics_file = output_path / f"corrected_icp_metrics_{ground_method}.json"
    with open(metrics_file, 'w') as f:
        json.dump(metrics, f, indent=2)
    print(f"Metrics: {metrics_file}")

print(f"\nCORRECTED ICP ALIGNMENT COMPLETED")
print(f"Expected improvement: 6.64-19.37m → {rmse:.3f}m")
if rmse < 1.0:
    print(f"SUCCESS: Achieved target RMSE < 1.0m")
elif rmse < 3.0:
    print(f"PARTIAL SUCCESS: Significant improvement but not optimal")
else:
    print(f"NEEDS WORK: Still high RMSE, coordinate correction may be needed")