# Papermill parameters - will be overridden during execution
import os
from pathlib import Path

ground_method = "ransac_pmf"  # Default: csf, pmf, ransac, ransac_pmf
site_name = "trino_enel"
project_type = "trino_enel"

# Define file paths
#source_file = Path(f"../../../data/processed/{site_name}/ground_segmentation/{ground_method}/trino_enel_nonground.ply")
#target_file = Path(f"../../../data/processed/{site_name}/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply")

source_file = Path(f"../../../data/processed/{site_name}/aligned_coordinates/{site_name}_drone_{ground_method}_corrected.ply")
target_file = Path(f"../../../data/processed/{site_name}/aligned_coordinates/{site_name}_ifc_corrected.ply")

output_dir = f"../../../data/output_runs/alignment_testing/nn/{ground_method}"
os.makedirs(output_dir, exist_ok=True)

# Execution flags
enable_visualization = True
save_intermediate = True
save_results = True

# Install required packages
#!pip install tensorflow open3d matplotlib laspy transforms3d scipy pandas mlflow

# --- Imports
# Import libraries
import tensorflow as tf
import numpy as np
import os
import matplotlib.pyplot as plt
import open3d as o3d
import laspy
import logging
import time
from pathlib import Path
from datetime import datetime
from scipy.spatial import cKDTree
import pandas as pd
import json
import transforms3d.euler as t3d

import mlflow
import mlflow.tensorflow
import mlflow.keras
MLFLOW_AVAILABLE = True

# Configure logging
import logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


# Set random seeds for reproducibility
np.random.seed(42)
tf.random.set_seed(42)

# Log environment details
logger.info("Neural Network Alignment Environment Initialized")
logger.info(f"TensorFlow version: {tf.__version__}")
logger.info(f"Open3D version: {o3d.__version__}")
logger.info(f"Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

logger.info(f"Configuration:")
logger.info(f"  Ground Method: {ground_method}")
logger.info(f"  Source File: {source_file}")
logger.info(f"  Target File: {target_file}")
logger.info(f"  Output Dir: {output_dir}")

# --- Hyperparameters
num_points = 1024
batch_size = 32
epochs = 50
learning_rate = 0.0001
validation_split = 0.2
early_stopping_patience = 15

# Model architecture
hidden_dims = [64, 128, 256, 512]
dropout_rate = 0.5

# Define the neural network model
class PointNetAlignment(tf.keras.Model):
    """
    PointNet-style neural network for point cloud alignment.
    Predicts 6-DOF transformation parameters (3 translation + 3 rotation).
    """
    
    def __init__(self, hidden_dims=[64, 128, 256, 512], dropout_rate=0.3):
        super(PointNetAlignment, self).__init__()
        
        self.hidden_dims = hidden_dims
        self.dropout_rate = dropout_rate
        
        # Point-wise feature extraction layers
        self.conv_layers = []
        for i, dim in enumerate(hidden_dims):
            self.conv_layers.append(
                tf.keras.layers.Conv1D(dim, 1, activation='relu', name=f'conv1d_{i}')
            )
            self.conv_layers.append(
                tf.keras.layers.BatchNormalization(name=f'bn_{i}')
            )
        
        # Global feature aggregation
        self.global_pool = tf.keras.layers.GlobalMaxPooling1D()
        
        # Regression head for transformation parameters
        #self.dense1 = tf.keras.layers.Dense(256, activation='relu')
        self.dense1 = tf.keras.layers.Dense(256, activation='relu', kernel_regularizer=tf.keras.regularizers.l2(0.01))
        self.dropout1 = tf.keras.layers.Dropout(dropout_rate)
        #self.dense2 = tf.keras.layers.Dense(128, activation='relu')
        self.dense2 = tf.keras.layers.Dense(128, activation='relu', kernel_regularizer=tf.keras.regularizers.l2(0.01))
        self.dropout2 = tf.keras.layers.Dropout(dropout_rate)
        
        # Output layer: 6 parameters (3 translation + 3 rotation)
        self.output_layer = tf.keras.layers.Dense(6, name='transformation_params')
    
    def call(self, inputs, training=None):
        """
        Forward pass of the network.
        """
        x = inputs
        
        # Point-wise feature extraction
        for layer in self.conv_layers:
            x = layer(x, training=training)
        
        # Global feature aggregation
        x = self.global_pool(x)
        
        # Regression head
        x = self.dense1(x)
        x = self.dropout1(x, training=training)
        x = self.dense2(x)
        x = self.dropout2(x, training=training)
        
        # Output transformation parameters
        transformation_params = self.output_layer(x)
        
        return transformation_params

def chamfer_distance_tf(source, target):
    """
    Compute Chamfer Distance between source and target point clouds
    using TensorFlow operations.
    """
    # Expand dims for pairwise distance calculation
    source_exp = tf.expand_dims(source, axis=2)  # (B, N, 1, 3)
    target_exp = tf.expand_dims(target, axis=1)  # (B, 1, N, 3)
    
    pairwise_dist = tf.norm(source_exp - target_exp, axis=-1)  # (B, N, N)

    # Find minimum distances
    min_dist_source = tf.reduce_min(pairwise_dist, axis=2)  # (B, N)
    min_dist_target = tf.reduce_min(pairwise_dist, axis=1)  # (B, N)
    
    # Compute chamfer distance
    chamfer_dist = tf.reduce_mean(min_dist_source) + tf.reduce_mean(min_dist_target)

    return chamfer_dist


from transforms3d.euler import euler2mat  

def apply_transformation(points, transform_params):
    """
    Apply predicted transformation (translation + rotation) to a point cloud.
    """
    translation = transform_params[:3]
    rotation_angles = transform_params[3:]
    # Convert to rotation matrix
    R = t3d.euler2mat(*rotation_angles, axes='sxyz')

    # Apply transformation
    transformed_points = np.dot(points, R.T) + translation
    
    return transformed_points


def apply_transformation_tf(points, transform_params):
    """
    Apply predicted transformation (translation + rotation) to a point cloud using TensorFlow.
    """
    # Extract translation and rotation parameters
    translation = transform_params[:, :3]  # (B, 3)
    rotation_angles = transform_params[:, 3:]  # (B, 3)
    
    # Convert Euler angles to rotation matrices using TensorFlow
    # This is a simplified approach - you may need a more robust implementation
    batch_size = tf.shape(points)[0]
    
    # Create rotation matrices for each sample in the batch
    cos_x = tf.cos(rotation_angles[:, 0])
    sin_x = tf.sin(rotation_angles[:, 0])
    cos_y = tf.cos(rotation_angles[:, 1])
    sin_y = tf.sin(rotation_angles[:, 1])
    cos_z = tf.cos(rotation_angles[:, 2])
    sin_z = tf.sin(rotation_angles[:, 2])
    
    # Rotation matrix around X axis
    R_x = tf.stack([
        tf.stack([tf.ones_like(cos_x), tf.zeros_like(cos_x), tf.zeros_like(cos_x)], axis=1),
        tf.stack([tf.zeros_like(cos_x), cos_x, -sin_x], axis=1),
        tf.stack([tf.zeros_like(cos_x), sin_x, cos_x], axis=1)
    ], axis=1)
    
    # Rotation matrix around Y axis
    R_y = tf.stack([
        tf.stack([cos_y, tf.zeros_like(cos_y), sin_y], axis=1),
        tf.stack([tf.zeros_like(cos_y), tf.ones_like(cos_y), tf.zeros_like(cos_y)], axis=1),
        tf.stack([-sin_y, tf.zeros_like(cos_y), cos_y], axis=1)
    ], axis=1)
    
    # Rotation matrix around Z axis
    R_z = tf.stack([
        tf.stack([cos_z, -sin_z, tf.zeros_like(cos_z)], axis=1),
        tf.stack([sin_z, cos_z, tf.zeros_like(cos_z)], axis=1),
        tf.stack([tf.zeros_like(cos_z), tf.zeros_like(cos_z), tf.ones_like(cos_z)], axis=1)
    ], axis=1)
    
    # Combined rotation matrix R = R_z * R_y * R_x
    R = tf.matmul(tf.matmul(R_z, R_y), R_x)
    
    # Apply rotation: points @ R^T + translation
    transformed_points = tf.matmul(points, R, transpose_b=True) + tf.expand_dims(translation, axis=1)
    
    return transformed_points


def alignment_loss(source_points, target_points, alpha=0.7):
    """
    Custom loss combining transformation parameter loss and alignment quality.
    """
    def loss_fn(y_true, y_pred):
        # Parameter loss (MSE on transformation parameters)
        param_loss = tf.reduce_mean(tf.square(y_true - y_pred))
        
        # Separate translation and rotation losses
        translation_loss = tf.reduce_mean(tf.square(y_true[:, :3] - y_pred[:, :3]))
        rotation_loss = tf.reduce_mean(tf.square(y_true[:, 3:] - y_pred[:, 3:]))
        
        # Transform source using predicted parameters
        source_transformed = apply_transformation_tf(source_points, y_pred)
        
        # Compute chamfer distance
        chamfer = chamfer_distance_tf(source_transformed, target_points)
        
        # Combined loss with weights
        total_loss = alpha * param_loss + (1 - alpha) * chamfer
        
        # Optional: log for debugging (remove in production)
        # tf.print("Translation loss:", translation_loss, "Rotation loss:", rotation_loss)
        
        return total_loss
    
    return loss_fn


def normalize_point_cloud(points):
    """
    Normalizes a point cloud to be centered at the origin and scaled within a unit sphere.
    """
    # Ensure points are float32
    points = points.astype(np.float32)

    # Center the point cloud at the origin
    centroid = np.mean(points, axis=0)
    centered = points - centroid
    
    # Scale the point cloud to fit inside a unit sphere
    furthest_distance = np.max(np.linalg.norm(centered, axis=1))
    if furthest_distance > 0:
        normalized = centered / furthest_distance
    else:
        normalized = centered
    
    return normalized, centroid, furthest_distance

def load_point_cloud(file_path, num_points):
    """
    Loads a point cloud from file and returns `num_points` randomly sampled + normalized points.
    """
    pc = o3d.io.read_point_cloud(str(file_path))
    points = np.asarray(pc.points)  # Ensure float32
    points = points.astype(np.float32)  # Ensure float32

    # Check for empty point cloud
    if len(points) == 0:
        raise ValueError(f"No points found in {file_path}")

    # Sample points based on availability
    if len(points) < num_points:
        logger.warning(f"Point cloud has only {len(points)} points, less than requested {num_points}")
        sampled = points  # Use all available points
    else:
        idx = np.random.choice(len(points), num_points, replace=False)
        sampled = points[idx]  # Randomly sample points

    # Normalize
    normalized, centroid, scale = normalize_point_cloud(sampled)

    logger.info(f"Loaded point cloud: {len(sampled)} points")
    logger.info(f"Original centroid: {centroid}")
    logger.info(f"Original scale: {scale}")
        
    return normalized, centroid, scale

logger.info("Loading point clouds...")

source_points, source_centroid, source_scale = load_point_cloud(source_file, num_points)
target_points, target_centroid, target_scale = load_point_cloud(target_file, num_points)

logger.info(f"Source points shape: {source_points.shape}")
logger.info(f"Target points shape: {target_points.shape}")


import numpy as np
from sklearn.neighbors import NearestNeighbors
import matplotlib.pyplot as plt

def verify_point_cloud_stats(source_points, target_points, k=10, overlap_threshold=0.05, visualize=False):
    """
    Checks point count, density, sparsity, scale, and overlap between two point clouds.
    Optionally visualizes point distributions and reports warnings.
    """
    def compute_density(pc):
        if len(pc) < k:
            return 0.0
        nbrs = NearestNeighbors(n_neighbors=k).fit(pc)
        distances, _ = nbrs.kneighbors(pc)
        return 1.0 / (np.mean(distances[:, -1]) + 1e-6)

    def get_bbox(pc):
        return np.max(pc, axis=0) - np.min(pc, axis=0)

    def compute_overlap(pc1, pc2, threshold):
        nbrs = NearestNeighbors(n_neighbors=1).fit(pc2)
        distances, _ = nbrs.kneighbors(pc1)
        return np.mean(distances < threshold)

    def summary(name, pc):
        print(f"\n{name} Stats:")
        print(f"  ➤ Point Count     : {len(pc)}")
        print(f"  ➤ Density (1/avg kNN dist) : {compute_density(pc):.4f}")
        print(f"  ➤ Bounding Box (X,Y,Z)     : {get_bbox(pc)}")
        if len(pc) < 100:
            print(f"  ⚠ Warning: Very sparse point cloud (< 100 points)")

    # Summarize source and target
    summary("Source", source_points)
    summary("Target", target_points)

    # Compute overlap
    overlap = compute_overlap(source_points, target_points, overlap_threshold)
    print(f"\nOverlap Ratio (source → target, threshold={overlap_threshold}): {overlap*100:.2f}%")
    if overlap < 0.3:
        print("  ⚠ Warning: Low overlap — alignment may be difficult")

    # Optional visualization
    if visualize:
        fig = plt.figure(figsize=(8, 4))
        ax = fig.add_subplot(121, projection='3d')
        ax.scatter(*source_points.T, s=1, c='r')
        ax.set_title('Source')
        ax = fig.add_subplot(122, projection='3d')
        ax.scatter(*target_points.T, s=1, c='b')
        ax.set_title('Target')
        plt.show()

verify_point_cloud_stats(source_points, target_points, visualize=True)

# Shape for TensorFlow: (batch_size, num_points, 3)
print(f"Creating training data with batch size: {batch_size}")

def generate_training_data(source_points, target_points, num_samples=1000):
    """Generate training pairs with known transformations"""

    X_train = []  # Will contain concatenated [source_transformed, target] pairs
    y_train = []  # Will contain ground truth transformation parameters
    
    for i in range(num_samples):
        # Generate random transformation parameters
        #translation = np.random.uniform(-0.5, 0.5, 3)
        #rotation = np.random.uniform(-np.pi/6, np.pi/6, 3)  # ±30 degrees
        
        translation = np.random.uniform(-0.1, 0.1, 3)  # Reduced from -0.5,0.5
        rotation = np.random.uniform(-np.pi/12, np.pi/12, 3)  # ±15 degrees (reduced from ±30)
        
        # Apply transformation to source to create misaligned version
        #R = t3d.euler2mat(*rotation, axes='sxyz')
        #source_transformed = np.dot(source_points, R.T) + translation
        
        # Apply transformation to source to create misaligned version
        R = t3d.euler2mat(*rotation, axes='sxyz')
        source_transformed = np.dot(source_points, R.T) + translation

        # Create training pair: concatenated source and target
        # Shape: (2*num_points, 3) - first num_points are source, next num_points are target
        # training_pair = np.concatenate([source_transformed, target_points], axis=0)

        # Add small amount of noise for robustness
        noise = np.random.normal(0, 0.005, source_transformed.shape)
        source_transformed += noise
        
        # Ground truth is the INVERSE transformation (to align source back to target)
        # This is what the network should learn to predict
        inverse_translation = -translation
        inverse_rotation = -rotation  # Simplified - for small angles this approximation works
        
        transform_params = np.concatenate([inverse_translation, inverse_rotation])
        
        #X_train.append(training_pair)
        X_train.append(source_transformed)
        y_train.append(transform_params)

        if (i + 1) % 200 == 0:
            logger.info(f"Generated {i + 1}/{num_samples} samples")

    
    return np.array(X_train), np.array(y_train)

def augment_training_data(X_train, y_train, augmentation_factor=2):
    """
    Add data augmentation to increase training diversity and robustness.
    """
    X_augmented = []
    y_augmented = []
    
    logger.info(f"Augmenting training data with factor {augmentation_factor}...")
    
    for i in range(len(X_train)):
        # Original sample
        X_augmented.append(X_train[i])
        y_augmented.append(y_train[i])
        
        # Generate augmented samples
        for aug_idx in range(augmentation_factor - 1):
            # Add small random noise to points
            noise_scale = 0.002  # Small noise to maintain point cloud structure
            noise = np.random.normal(0, noise_scale, X_train[i].shape)
            X_aug = X_train[i] + noise
            
            # Add small perturbation to transformation parameters
            param_noise = np.random.normal(0, 0.01, y_train[i].shape)
            y_aug = y_train[i] + param_noise
            
            X_augmented.append(X_aug)
            y_augmented.append(y_aug)
    
    logger.info(f"Augmented dataset: {len(X_augmented)} samples (from {len(X_train)})")
    return np.array(X_augmented), np.array(y_augmented)


# Use this instead:
#X_train, y_train = generate_training_data(source_points, target_points)
#print(f"X_train shape: {X_train.shape}")
#print(f"y_train shape: {y_train.shape}")

X_train, y_train = generate_training_data(source_points, target_points)
print(f"Corrected X_train shape: {X_train.shape}")  # Should be (1000, 1024, 3)
print(f"Corrected y_train shape: {y_train.shape}")  # Should be (1000, 6)

# Apply data augmentation
X_train_aug, y_train_aug = augment_training_data(X_train, y_train, augmentation_factor=3)

# Training splits with augmented data
split_idx = int(len(X_train_aug) * (1 - validation_split))
X_train_split = X_train_aug[:split_idx]
y_train_split = y_train_aug[:split_idx]
X_val = X_train_aug[split_idx:]
y_val = y_train_aug[split_idx:]

logger.info(f"Final training set: {X_train_split.shape[0]} samples")
logger.info(f"Final validation set: {X_val.shape[0]} samples")

# Split into train and validation
# split_idx = int(len(X_train) * (1 - validation_split))
# X_train_split = X_train[:split_idx]
# y_train_split = y_train[:split_idx]
# X_val = X_train[split_idx:]
# y_val = y_train[split_idx:]

# logger.info(f"Training set: {X_train_split.shape[0]} samples")
# logger.info(f"Validation set: {X_val.shape[0]} samples")

def create_model(source_points, target_points, hidden_dims=None, dropout_rate=0.1, learning_rate=0.001):
    """
    Create and compile the neural network model.
    """
    model = PointNetAlignment(
        hidden_dims=hidden_dims,
        dropout_rate=dropout_rate
    )

    # Create the custom loss function with the point clouds
    custom_loss = alignment_loss(source_points, target_points, alpha=0.7)

    # Compile model with MSE loss and MAE metric (Chamfer loss not used here)
    model.compile(
        optimizer=tf.keras.optimizers.Adam(learning_rate=learning_rate),
        loss=  custom_loss, #'mse', #chamfer_distance_tf, #
        metrics=['mae']
    )
    return model

logger.info("Creating neural network model...")

model = create_model(
    source_points=source_points,
    target_points=target_points,
    hidden_dims=hidden_dims,
    dropout_rate=dropout_rate,
    learning_rate=learning_rate
)
dummy_input = tf.random.uniform((1, num_points, 3)) 
_ = model(dummy_input)  

# Now show summary
model.summary()

# Log parameter count
total_params = model.count_params()
logger.info(f"Total model parameters: {total_params:,}")


mlflow.set_tracking_uri("file:../../../data/mlruns")  # or your remote server URI
mlflow.set_experiment("Neural_Network_PointCloud_Alignment")

alignment_method = "nn"

if mlflow.active_run() is not None:
    run_context = mlflow.start_run(nested=True)
else:
    run_context = mlflow.start_run()

with run_context:
    mlflow.log_param("total_params", total_params)
    mlflow.tensorflow.autolog()  # or mlflow.keras.autolog()

    # Optionally log input parameters manually (for better clarity)
    mlflow.log_params({
        "site_name": site_name,
        "ground_method": ground_method,
        "num_points": num_points,
        "batch_size": batch_size,
        "epochs": epochs,
        "learning_rate": learning_rate,
        "dropout_rate": dropout_rate,
        "model_type": "PointNetAlignment"
    })
    
    # Log point cloud metadata
    mlflow.log_param("source_file", str(source_file))
    mlflow.log_param("target_file", str(target_file))


# Setup callbacks
callbacks = []

# Early stopping
early_stopping = tf.keras.callbacks.EarlyStopping(
    monitor='val_loss',
    patience=early_stopping_patience,
    restore_best_weights=True,
    verbose=1
)
callbacks.append(early_stopping)

# Model checkpointing
checkpoint_path = os.path.join(output_dir, "best_model.h5")
model_checkpoint = tf.keras.callbacks.ModelCheckpoint(
    checkpoint_path,
    monitor='val_loss',
    save_best_only=True,
    verbose=1
)
callbacks.append(model_checkpoint)

# Learning rate reduction
lr_reduction = tf.keras.callbacks.ReduceLROnPlateau(
    monitor='val_loss',
    factor=0.5,
    patience=5,
    min_lr=1e-6,
    verbose=1
)
callbacks.append(lr_reduction)

logger.info("Starting model training...")
start_time = time.time()

history = model.fit(
    X_train_split,
    y_train_split,
    batch_size=batch_size,
    epochs=epochs,
    validation_data=(X_val, y_val),
    verbose=1,
    callbacks=callbacks
)

end_time = time.time()
training_time = round(end_time - start_time, 2)



logger.info(f"Training completed in {training_time} seconds")

# Save final model
model_path = os.path.join(output_dir, "pointnet_alignment_model.h5")
model.save(model_path)
logger.info(f"Saved trained model to {model_path}")

def evaluate_alignment(model, source_points, target_points, source_centroid, source_scale):
    """
    Evaluate alignment quality using the trained model.
    """
    logger.info("Evaluating alignment quality...")
    
    # Predict transformation
    source_batch = np.expand_dims(source_points, 0)
    pred_transform = model.predict(source_batch, verbose=0)[0]
    
    # Apply transformation
    aligned_points = apply_transformation(source_points, pred_transform)
    
    # Denormalize points for evaluation
    aligned_points = aligned_points * source_scale + source_centroid
    
    # Load target points for comparison (denormalized)
    target_pc = o3d.io.read_point_cloud(str(target_file))
    target_full = np.asarray(target_pc.points)
    
    if len(target_full) == 0:
        logger.warning("Target point cloud is empty, using dummy evaluation")
        return {
            'rmse': 999.0,
            'mean_distance': 999.0,
            'predicted_transform': pred_transform,
            'alignment_quality': 'poor'
        }
    
    # Compute alignment metrics
    tree = cKDTree(target_full)
    distances, _ = tree.query(aligned_points)
    
    rmse = np.sqrt(np.mean(distances**2))
    mean_distance = np.mean(distances)
    median_distance = np.median(distances)
    
    # Determine alignment quality
    if rmse < 0.1:
        quality = 'excellent'
    elif rmse < 0.5:
        quality = 'good'
    elif rmse < 1.0:
        quality = 'fair'
    else:
        quality = 'poor'
    
    logger.info(f"Alignment evaluation:")
    logger.info(f"  RMSE: {rmse:.4f}")
    logger.info(f"  Mean distance: {mean_distance:.4f}")
    logger.info(f"  Median distance: {median_distance:.4f}")
    logger.info(f"  Quality: {quality}")
    
    mlflow.log_metric("eval_rmse", rmse)
    mlflow.log_metric("eval_mean_distance", mean_distance)
    mlflow.log_metric("eval_median_distance", median_distance)

    return {
        'rmse': rmse,
        'mean_distance': mean_distance,
        'median_distance': median_distance,
        'predicted_transform': pred_transform,
        'alignment_quality': quality
    }

# Evaluate the trained model
evaluation_results = evaluate_alignment(
    model, source_points, target_points, 
    source_centroid, source_scale
)

logger.info("Model evaluation completed")

final_loss = history.history['loss'][-1]
final_val_loss = history.history['val_loss'][-1]
final_mae = history.history['mae'][-1]
eval_rmse = evaluation_results.get('rmse', 999.0)

logger.info(f"Final Loss: {final_loss:.6f}")
logger.info(f"Final Validation Loss: {final_val_loss:.6f}")
logger.info(f"Final MAE: {final_mae:.6f}")
logger.info(f"Evaluation RMSE: {eval_rmse:.6f}")

# Test on validation set
logger.info("Testing on validation set...")
val_predictions = model.predict(X_val, verbose=0)
val_loss = np.mean(np.square(y_val - val_predictions))
val_mae = np.mean(np.abs(y_val - val_predictions))

logger.info(f"Validation Loss: {val_loss:.6f}")
logger.info(f"Validation MAE: {val_mae:.6f}")

# ## Training Metrics Visualization
def plot_training_curves(history, output_dir):
    plt.figure(figsize=(12, 5))
    epochs = range(1, len(history.history['loss']) + 1)
    plt.subplot(1, 2, 1)
    plt.plot(epochs, history.history['loss'], label='Training Loss', marker='o')

    if 'val_loss' in history.history:
        plt.plot(epochs, history.history['val_loss'], label='Validation Loss', marker='x')
    plt.xlabel('Epoch')
    plt.ylabel('MSE Loss')
    plt.title('Loss Over Epochs')
    plt.legend()
    plt.grid(True)
    plt.subplot(1, 2, 2)
    plt.plot(epochs, history.history['mae'], label='Training MAE', marker='o')
    
    if 'val_mae' in history.history:
        plt.plot(epochs, history.history['val_mae'], label='Validation MAE', marker='x')
    plt.xlabel('Epoch')
    plt.ylabel('MAE')
    plt.title('Mean Absolute Error Over Epochs')
    plt.legend()
    plt.grid(True)
    plt.tight_layout()
    plot_path = os.path.join(output_dir, "training_metrics.png")
    plt.savefig(plot_path)
    plt.show()
    plt.close()
    logger.info(f"Saved training plot: {plot_path}")

# Call the plot function
plot_training_curves(history, output_dir)


def visualize_alignment(source, target, aligned, output_dir):
    """Visualize alignment results"""
    fig = plt.figure(figsize=(15, 5))
    
    # Original source
    ax1 = fig.add_subplot(131, projection='3d')
    ax1.scatter(source[:, 0], source[:, 1], source[:, 2], c='red', s=1)
    ax1.set_title('Source Point Cloud')
    
    # Target
    ax2 = fig.add_subplot(132, projection='3d')
    ax2.scatter(target[:, 0], target[:, 1], target[:, 2], c='blue', s=1)
    ax2.set_title('Target Point Cloud')
    
    # Aligned result
    ax3 = fig.add_subplot(133, projection='3d')
    ax3.scatter(aligned[:, 0], aligned[:, 1], aligned[:, 2], c='green', s=1)
    ax3.scatter(target[:, 0], target[:, 1], target[:, 2], c='blue', s=1, alpha=0.5)
    ax3.set_title('Aligned Result')
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, "alignment_visualization.png"))
    plt.show()

# Apply transformation to get aligned points
source_batch = np.expand_dims(source_points, 0)
pred_transform = model.predict(source_batch, verbose=0)[0]
aligned_source = apply_transformation(source_points, pred_transform)

# Visualize alignment results
visualize_alignment(source_points, target_points, aligned_source, output_dir)

def plot_transformation_analysis(y_true, y_pred, output_dir):
    """
    Analyze predicted vs true transformation parameters.
    """
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    
    labels = ['TX', 'TY', 'TZ', 'RX', 'RY', 'RZ']
    
    for i in range(6):
        row = i // 3
        col = i % 3
        
        axes[row, col].scatter(y_true[:, i], y_pred[:, i], alpha=0.6)
        axes[row, col].plot([y_true[:, i].min(), y_true[:, i].max()], 
                           [y_true[:, i].min(), y_true[:, i].max()], 'r--')
        axes[row, col].set_xlabel(f'True {labels[i]}')
        axes[row, col].set_ylabel(f'Predicted {labels[i]}')
        axes[row, col].set_title(f'{labels[i]} Predictions')
        axes[row, col].grid(True)
    
    plt.tight_layout()
    analysis_path = os.path.join(output_dir, "transformation_analysis.png")
    plt.savefig(analysis_path, dpi=300, bbox_inches='tight')
    plt.show()
    
    logger.info(f"Transformation analysis saved to: {analysis_path}")

# Plot transformation analysis
plot_transformation_analysis(y_val, val_predictions, output_dir)

def log_and_export_alignment_results(
    ground_method,
    alignment_method,
    results_dict,
    output_dir,
    source_file,
    target_file,
    site_name,
    model=None,
    export_json=True,
):
    """Logs alignment results to MLflow and optionally exports to JSON/CSV."""
    timestamp = datetime.now().isoformat()
    results_data = {
        "ground_method": ground_method,
        "alignment_method": alignment_method,
        "site_name": site_name,
        "timestamp": timestamp,
        "source_file": str(source_file),
        "target_file": str(target_file),
        **results_dict,
    }

    # Start run safely (nested if needed)
    if mlflow.active_run():
        run = mlflow.start_run(nested=True)
    else:
        run = mlflow.start_run()

    with run:
        # Log parameters
        mlflow.log_params({
            "ground_method": ground_method,
            "alignment_method": alignment_method,
            "site_name": site_name,
            "source_file": str(source_file),
            "target_file": str(target_file),
        })

        # Log metrics
        mlflow.log_metrics({
            "final_loss": results_dict.get("final_loss", 999.0),
            "final_val_loss": results_dict.get("final_val_loss", 999.0),
            "final_mae": results_dict.get("final_mae", 999.0),
            "eval_rmse": results_dict.get("eval_rmse", 999.0),
            "epochs_completed": results_dict.get("epochs_completed", 0),
            "model_parameters": results_dict.get("model_parameters", 0),
        })

        # Log model if provided
        if model:
            mlflow.keras.log_model(model, artifact_path="model")

        # Log visualizations
        for img_name in [
            "training_metrics.png",
            "alignment_visualization.png",
            "transformation_analysis.png",
        ]:
            img_path = Path(output_dir) / img_name
            if img_path.exists():
                mlflow.log_artifact(str(img_path))

        # Export to JSON and CSV
        if export_json:
            results_dir = Path(output_dir) / "results"
            results_dir.mkdir(parents=True, exist_ok=True)
            result_file = results_dir / f"{alignment_method}_{ground_method}_results.json"

            with open(result_file, "w") as f:
                #json.dump(results_data, f, indent=2)
                json.dump(results_data, f, indent=2, default=str)

            mlflow.log_artifact(str(result_file))

            # Append to master CSV
            master_file = Path(output_dir).parent / "master_results.csv"
            df_row = pd.DataFrame([results_data])

            if master_file.exists():
                df_existing = pd.read_csv(master_file)
                df_combined = pd.concat([df_existing, df_row], ignore_index=True)
            else:
                df_combined = df_row

            df_combined.to_csv(master_file, index=False)
            mlflow.log_artifact(str(master_file))

    return results_data


exported_results = log_and_export_alignment_results(
    ground_method=ground_method,
    alignment_method="neural_network",
    results_dict=evaluation_results,
    output_dir=output_dir,
    source_file=source_file,
    target_file=target_file,
    site_name=site_name,
    model=model,
    export_json=True
)
