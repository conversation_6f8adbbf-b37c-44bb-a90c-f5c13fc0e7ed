# Parameters (Papermill)
site_name = "trino_enel"
ground_method = "csf"  # Options: csf, pmf, ransac
confidence_threshold = 0.6  # Lower threshold for C-sections (harder to detect)
output_dir = "../../../../../data/output_runs/pile_detection"
use_aligned_data = True  # Use ICP aligned point clouds if available
enable_visualization = True
save_results = True

# Imports
import numpy as np
import open3d as o3d
import matplotlib.pyplot as plt
from pathlib import Path
import pandas as pd
from sklearn.cluster import DBSCAN
from sklearn.neighbors import NearestNeighbors
from sklearn.decomposition import PCA
import json
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Deep learning imports
try:
    import torch
    import torch.nn as nn
    import torch.nn.functional as F
    from torch.utils.data import Dataset, DataLoader
    TORCH_AVAILABLE = True
    print(f"PyTorch version: {torch.__version__}")
    print(f"CUDA available: {torch.cuda.is_available()}")
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
except ImportError:
    TORCH_AVAILABLE = False
    print("PyTorch not available - using geometric detection only")
    device = None

# Setup
output_path = Path(output_dir) / ground_method
output_path.mkdir(parents=True, exist_ok=True)

print(f"C-SECTION PILE DETECTION - {ground_method.upper()}")
print(f"Site: {site_name}")
print(f"Output: {output_path}")
print(f"Use aligned data: {'Enabled' if use_aligned_data else '❌ Disabled'}")
print(f"Confidence threshold: {confidence_threshold}")


!ls - lh ../../../../../data

from pathlib import Path
import numpy as np
import open3d as o3d

# Load point cloud data, preferring aligned ICP output if available
print("==> LOADING POINT CLOUD DATA")

aligned_file = Path(f"../../../../../data/output_runs/icp_alignment_corrected/{ground_method}/aligned_ifc_{ground_method}.ply")
if aligned_file.exists():
    input_file = aligned_file
    data_source = "ICP aligned"
    print(f"Using aligned point cloud: {input_file}")
else:
    print(f"Aligned file not found: {aligned_file}")
    print("Falling back to ground segmentation data")
    input_file = Path(f"../../../../../data/ground_segmentation/{ground_method}/ifc_ground.ply")
    data_source = "Ground segmentation"

# Load point cloud
pcd = o3d.io.read_point_cloud(str(input_file))
points = np.asarray(pcd.points)

# Display summary
print(f"\nDATA SOURCE: {data_source}")
print(f"Points loaded: {points.shape[0]:,}")
print(f"Bounds: X[{points[:, 0].min():.1f}, {points[:, 0].max():.1f}] "
      f"Y[{points[:, 1].min():.1f}, {points[:, 1].max():.1f}] "
      f"Z[{points[:, 2].min():.1f}, {points[:, 2].max():.1f}]")


if TORCH_AVAILABLE:
    # Simple PointNet++ for C-section detection
    class SimplePointNet(nn.Module):
        """Simplified PointNet for C-section pile detection"""
        def __init__(self, num_points=1024, num_classes=2):
            super(SimplePointNet, self).__init__()
            self.num_points = num_points
            
            # Point feature extraction
            self.conv1 = nn.Conv1d(3, 64, 1)
            self.conv2 = nn.Conv1d(64, 128, 1)
            self.conv3 = nn.Conv1d(128, 256, 1)
            
            self.bn1 = nn.BatchNorm1d(64)
            self.bn2 = nn.BatchNorm1d(128)
            self.bn3 = nn.BatchNorm1d(256)
            
            # Classification head
            self.fc1 = nn.Linear(256, 128)
            self.fc2 = nn.Linear(128, 64)
            self.fc3 = nn.Linear(64, num_classes)
            
            self.dropout = nn.Dropout(0.3)
            
        def forward(self, x):
            # x: [batch_size, 3, num_points]
            x = F.relu(self.bn1(self.conv1(x)))
            x = F.relu(self.bn2(self.conv2(x)))
            x = F.relu(self.bn3(self.conv3(x)))
            
            # Global max pooling
            x = torch.max(x, 2)[0]  # [batch_size, 256]
            
            # Classification
            x = F.relu(self.fc1(x))
            x = self.dropout(x)
            x = F.relu(self.fc2(x))
            x = self.dropout(x)
            x = self.fc3(x)
            
            return F.log_softmax(x, dim=1)
    
    print("Simple PointNet model implemented")
else:
    print("Skipping PointNet implementation - PyTorch not available")

# Training utilities
if TORCH_AVAILABLE:
    def create_synthetic_training_data(num_samples=1000, num_points=512):
        """Create synthetic training data for C-section detection"""
        print(f"Creating {num_samples} synthetic training samples...")
        
        X = []
        y = []
        
        for i in range(num_samples):
            if i % 2 == 0:
                # Generate C-section like pattern
                points = generate_c_section_pattern(num_points)
                label = 1  # C-section
            else:
                # Generate random/background pattern
                points = np.random.randn(num_points, 3) * 0.5
                label = 0  # Not C-section
            
            X.append(points)
            y.append(label)
        
        return np.array(X), np.array(y)
    
    def generate_c_section_pattern(num_points):
        """Generate a synthetic C-section pattern"""
        points = []
        
        # C-section has two flanges and a web
        # Top flange
        for i in range(num_points // 3):
            x = np.random.uniform(-0.1, 0.1)
            y = np.random.uniform(-0.05, 0.05)
            z = np.random.uniform(0.8, 1.0)
            points.append([x, y, z])
        
        # Bottom flange
        for i in range(num_points // 3):
            x = np.random.uniform(-0.1, 0.1)
            y = np.random.uniform(-0.05, 0.05)
            z = np.random.uniform(0.0, 0.2)
            points.append([x, y, z])
        
        # Web (connecting part)
        for i in range(num_points - 2 * (num_points // 3)):
            x = np.random.uniform(-0.02, 0.02)
            y = np.random.uniform(-0.05, 0.05)
            z = np.random.uniform(0.2, 0.8)
            points.append([x, y, z])
        
        points = np.array(points)
        # Add some noise
        points += np.random.normal(0, 0.01, points.shape)
        
        return points
    
    print("Training utilities implemented")
else:
    print("Skipping training utilities - PyTorch not available")

if TORCH_AVAILABLE:
    # Simple dataset class
    class CSectionDataset(Dataset):
        def __init__(self, X, y):
            self.X = torch.FloatTensor(X)
            self.y = torch.LongTensor(y)
        
        def __len__(self):
            return len(self.X)
        
        def __getitem__(self, idx):
            # Return [3, N] format for PointNet
            return self.X[idx].transpose(0, 1), self.y[idx]
    
    # Training function
    def train_model(model, train_loader, num_epochs=10):
        """Train the PointNet model"""
        optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
        criterion = nn.NLLLoss()
        
        model.train()
        for epoch in range(num_epochs):
            total_loss = 0
            correct = 0
            total = 0
            
            for batch_idx, (data, target) in enumerate(train_loader):
                data, target = data.to(device), target.to(device)
                
                optimizer.zero_grad()
                output = model(data)
                loss = criterion(output, target)
                loss.backward()
                optimizer.step()
                
                total_loss += loss.item()
                pred = output.argmax(dim=1)
                correct += pred.eq(target).sum().item()
                total += target.size(0)
            
            accuracy = 100. * correct / total
            avg_loss = total_loss / len(train_loader)
            print(f'Epoch {epoch+1}/{num_epochs}: Loss: {avg_loss:.4f}, Accuracy: {accuracy:.2f}%')
        
        return model
    
    print("Training utilities ready")
else:
    print("Skipping training setup - PyTorch not available")

class GeometricCSectionDetector:
    """Geometric-based C-section pile detection using point cloud analysis."""
    
    def __init__(self):
        # C-section pile characteristics
        self.min_height = 1.5  # Minimum pile height above ground
        self.max_height = 10.0  # Maximum reasonable pile height
        self.typical_width = 0.15  # Typical C-section width (~15cm)
        self.typical_depth = 0.08  # Typical C-section depth (~8cm)
        self.width_tolerance = 0.08  # ±8cm tolerance
        self.min_points_per_pile = 40  # Minimum points (C-sections have fewer points than I-sections)
        
    def detect_vertical_structures(self, points):
        """Detect vertical structures that could be C-section piles."""
        print("Detecting vertical structures...")
        
        # Filter by height
        z_min = points[:, 2].min()
        height_mask = (points[:, 2] - z_min) >= self.min_height
        elevated_points = points[height_mask]
        
        print(f"Points above {self.min_height}m: {len(elevated_points):,}")
        
        if len(elevated_points) < self.min_points_per_pile:
            return []
        
        # Use smaller clustering radius for C-sections (they're thinner)
        clustering = DBSCAN(eps=0.3, min_samples=self.min_points_per_pile)
        cluster_labels = clustering.fit_predict(elevated_points[:, :2])
        
        # Extract clusters
        unique_labels = set(cluster_labels)
        unique_labels.discard(-1)  # Remove noise
        
        vertical_structures = []
        for label in unique_labels:
            cluster_mask = cluster_labels == label
            cluster_points = elevated_points[cluster_mask]
            
            if len(cluster_points) >= self.min_points_per_pile:
                vertical_structures.append(cluster_points)
        
        print(f"Found {len(vertical_structures)} vertical structure candidates")
        return vertical_structures
    
    def analyze_c_section_characteristics(self, structure_points):
        """Analyze if structure has C-section characteristics."""
        # Calculate structure dimensions
        x_span = structure_points[:, 0].max() - structure_points[:, 0].min()
        y_span = structure_points[:, 1].max() - structure_points[:, 1].min()
        z_span = structure_points[:, 2].max() - structure_points[:, 2].min()
        
        # C-section characteristics:
        # 1. Significant height (vertical structure)
        # 2. Moderate width (C-section opening)
        # 3. Smaller depth (C-section profile)
        # 4. Asymmetric cross-section (C-shape)
        
        height_score = min(z_span / self.min_height, 1.0)
        
        # Check dimensions for C-section profile
        max_horizontal = max(x_span, y_span)
        min_horizontal = min(x_span, y_span)
        
        # C-sections are typically smaller than I-sections
        width_score = 0.0
        if (self.typical_width - self.width_tolerance) <= max_horizontal <= (self.typical_width + self.width_tolerance):
            width_score = 0.7
        
        # C-sections have moderate aspect ratio (not as elongated as I-sections)
        aspect_ratio = max_horizontal / (min_horizontal + 1e-6)
        aspect_score = 0.0
        if 1.5 <= aspect_ratio <= 4.0:  # C-sections less elongated than I-sections
            aspect_score = 0.8
        
        # Analyze point distribution for C-shape characteristics
        distribution_score = self.analyze_point_distribution(structure_points)
        
        # Combine scores (weight distribution more heavily for C-sections)
        confidence = (height_score * 0.3 + width_score * 0.3 + 
                     aspect_score * 0.2 + distribution_score * 0.2)
        
        return {
            'confidence': confidence,
            'height': z_span,
            'width': max_horizontal,
            'depth': min_horizontal,
            'aspect_ratio': aspect_ratio,
            'point_count': len(structure_points),
            'distribution_score': distribution_score
        }
    
    def analyze_point_distribution(self, structure_points):
        """Analyze point distribution to detect C-shape characteristics."""
        if len(structure_points) < 20:
            return 0.0
        
        # Project points to horizontal plane and analyze distribution
        xy_points = structure_points[:, :2]
        
        # Center the points
        centroid = xy_points.mean(axis=0)
        centered_points = xy_points - centroid
        
        # Use PCA to find principal directions
        pca = PCA(n_components=2)
        pca.fit(centered_points)
        
        # Transform to principal component space
        transformed_points = pca.transform(centered_points)
        
        # Analyze distribution along principal axes
        pc1_std = np.std(transformed_points[:, 0])
        pc2_std = np.std(transformed_points[:, 1])
        
        # C-sections should have asymmetric distribution
        asymmetry_ratio = max(pc1_std, pc2_std) / (min(pc1_std, pc2_std) + 1e-6)
        
        # Score based on asymmetry (C-sections are more asymmetric than cylinders)
        if asymmetry_ratio > 2.0:
            return 0.8
        elif asymmetry_ratio > 1.5:
            return 0.6
        else:
            return 0.3
    
    def extract_pile_center(self, structure_points):
        """Extract pile center coordinates."""
        # Use centroid of bottom 20% of points
        z_min = structure_points[:, 2].min()
        z_threshold = z_min + 0.2 * (structure_points[:, 2].max() - z_min)
        
        base_mask = structure_points[:, 2] <= z_threshold
        base_points = structure_points[base_mask]
        
        if len(base_points) > 0:
            center = base_points.mean(axis=0)
        else:
            center = structure_points.mean(axis=0)
        
        return center
    
    def detect_c_section_piles(self, points):
        """Main detection pipeline."""
        print("Starting C-section pile detection...")
        
        # Step 1: Find vertical structures
        vertical_structures = self.detect_vertical_structures(points)
        
        if not vertical_structures:
            print("No vertical structures found")
            return []
        
        # Step 2: Analyze each structure for C-section characteristics
        detections = []
        for i, structure in enumerate(vertical_structures):
            analysis = self.analyze_c_section_characteristics(structure)
            
            if analysis['confidence'] >= confidence_threshold:
                center = self.extract_pile_center(structure)
                
                detection = {
                    'x': float(center[0]),
                    'y': float(center[1]),
                    'z': float(center[2]),
                    'confidence': float(analysis['confidence']),
                    'width': float(analysis['width']),
                    'height': float(analysis['height']),
                    'depth': float(analysis['depth']),
                    'c_section_score': float(analysis['confidence']),
                    'point_count': int(analysis['point_count']),
                    'distribution_score': float(analysis['distribution_score']),
                    'detection_method': 'geometric'
                }
                
                detections.append(detection)
        
        print(f"Detected {len(detections)} C-section pile candidates")
        return detections

# Initialize detector
detector = GeometricCSectionDetector()
print("Geometric C-section detector initialized")

if TORCH_AVAILABLE:
    print("==> TRAINING POINTNET MODEL")
    
    # Create synthetic training data
    X_train, y_train = create_synthetic_training_data(num_samples=200, num_points=512)
    print(f"Training data shape: {X_train.shape}")
    print(f"Labels shape: {y_train.shape}")
    print(f"C-section samples: {np.sum(y_train == 1)}")
    print(f"Background samples: {np.sum(y_train == 0)}")
    
    # Create dataset and dataloader
    train_dataset = CSectionDataset(X_train, y_train)
    train_loader = DataLoader(train_dataset, batch_size=16, shuffle=True)
    
    # Initialize model
    model = SimplePointNet(num_points=512, num_classes=2)
    model.to(device)
    print(f"Model initialized on {device}")
    
    # Train the model
    print("\nStarting training...")
    trained_model = train_model(model, train_loader, num_epochs=5)
    
    print("\nTraining completed!")
    
    # Test on a few samples
    model.eval()
    with torch.no_grad():
        test_samples = 5
        test_data = torch.FloatTensor(X_train[:test_samples]).transpose(1, 2).to(device)
        test_labels = y_train[:test_samples]
        
        outputs = model(test_data)
        predictions = outputs.argmax(dim=1).cpu().numpy()
        
        print(f"\nTest Results:")
        for i in range(test_samples):
            print(f"  Sample {i+1}: True={test_labels[i]}, Predicted={predictions[i]}")
    
    # Store results for later use
    pointnet_results = {
        'model': trained_model,
        'training_accuracy': 'See training log above',
        'detections': []  # Would be filled during inference
    }
    
else:
    print("Skipping PointNet training - PyTorch not available")
    pointnet_results = {'detections': [], 'model': None}

# Run geometric detection
geometric_detections = detector.detect_c_section_piles(points)

print(f"\n==> DETECTION RESULTS COMPARISON")
print(f"\nGeometric Method:")
print(f"  Total detections: {len(geometric_detections)}")
print(f"  Confidence threshold: {confidence_threshold}")

if TORCH_AVAILABLE:
    print(f"\nPointNet Method:")
    print(f"  Model trained: {'Yes' if pointnet_results.get('model') else 'No'}")
    print(f"  Ready for inference: {'Yes' if pointnet_results.get('model') else 'No'}")

# For this demo, use geometric detections as the primary method
# In practice, you would use the trained PointNet model for inference
if len(geometric_detections) > 0:
    final_detections = geometric_detections
    detection_method_used = 'geometric'
    print(f"\nUsing geometric detections for final results")
    if TORCH_AVAILABLE and pointnet_results.get('model'):
        print(f"Note: Trained PointNet model is available for future inference")
else:
    final_detections = []
    detection_method_used = 'none'
    print(f"\nNo detections found")
    if TORCH_AVAILABLE and pointnet_results.get('model'):
        print(f"Note: Trained PointNet model is available for inference on real data")

if final_detections:
    confidences = [d['confidence'] for d in final_detections]
    heights = [d.get('height', 2.0) for d in final_detections]
    widths = [d.get('width', 0.15) for d in final_detections]
    
    print(f"\nFinal Detection Statistics ({detection_method_used}):")
    print(f"  Count: {len(final_detections)}")
    print(f"  Mean confidence: {np.mean(confidences):.3f}")
    print(f"  Confidence range: [{np.min(confidences):.3f}, {np.max(confidences):.3f}]")
    print(f"  Height range: [{np.min(heights):.2f}, {np.max(heights):.2f}] m")
    print(f"  Width range: [{np.min(widths):.2f}, {np.max(widths):.2f}] m")
    
    # Display individual detections
    print(f"\nDetailed Results:")
    for i, det in enumerate(final_detections[:10]):  # Show first 10
        print(f"  Pile {i+1}: ({det['x']:.1f}, {det['y']:.1f}, {det['z']:.1f}) "
              f"conf={det['confidence']:.3f} method={det.get('detection_method', 'unknown')}")
    if len(final_detections) > 10:
        print(f"  ... and {len(final_detections) - 10} more")
else:
    print("\nNo C-section piles detected with either method")
    print("Consider:")
    print("  - Lowering confidence threshold")
    print("  - Adjusting patch size for PointNet++")
    print("  - Training PointNet++ model on labeled data")

if enable_visualization and final_detections:
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # Plot 1: Top view with detections
    ax1.scatter(points[:, 0], points[:, 1], c=points[:, 2], s=1, alpha=0.6, cmap='viridis')
    
    # Overlay detections (use squares for C-sections to distinguish from I-sections)
    for det in final_detections:
        square = plt.Rectangle((det['x'] - det['width']/2, det['y'] - det['width']/2), 
                             det['width'], det['width'], 
                             fill=False, color='orange', linewidth=2)
        ax1.add_patch(square)
        ax1.text(det['x'], det['y'], f"{det['confidence']:.2f}", 
                ha='center', va='center', color='orange', fontweight='bold')
    
    ax1.set_xlabel('X (m)')
    ax1.set_ylabel('Y (m)')
    ax1.set_title(f'C-Section Pile Detection - Top View\n{len(final_detections)} detections')
    ax1.axis('equal')
    ax1.grid(True, alpha=0.3)
    
    # Plot 2: Side view
    ax2.scatter(points[:, 0], points[:, 2], c='blue', s=1, alpha=0.6)
    
    # Overlay detections
    for det in final_detections:
        ax2.scatter(det['x'], det['z'], c='orange', s=100, marker='s', linewidth=3)
        ax2.text(det['x'], det['z'] + 0.5, f"{det['confidence']:.2f}", 
                ha='center', va='bottom', color='orange', fontweight='bold')
    
    ax2.set_xlabel('X (m)')
    ax2.set_ylabel('Z (m)')
    ax2.set_title('C-Section Pile Detection - Side View')
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    if save_results:
        plt.savefig(output_path / f'c_section_detection_{ground_method}.png', dpi=300, bbox_inches='tight')
        print(f"Saved visualization: {output_path / f'c_section_detection_{ground_method}.png'}")
    
    plt.show()
else:
    print("No detections to visualize")

if save_results:
    print("\nSAVING RESULTS")
    
    # Save detections CSV
    if final_detections:
        df = pd.DataFrame(final_detections)
        csv_file = output_path / f'c_section_detections_{ground_method}.csv'
        df.to_csv(csv_file, index=False)
        print(f"Saved detections: {csv_file}")
    
    # Save metrics
    metrics = {
        'timestamp': datetime.now().isoformat(),
        'site_name': site_name,
        'ground_method': ground_method,
        'detection_method_used': detection_method_used,
        'pytorch_available': TORCH_AVAILABLE,
        'pile_type': 'c_section',
        'confidence_threshold': confidence_threshold,
        'total_detections': len(final_detections),
        'input_points': int(len(points)),
        'data_source': data_source,
        'use_aligned_data': use_aligned_data,
        'input_file': str(input_file),
        'detection_statistics': {
            'mean_confidence': float(np.mean([d['confidence'] for d in final_detections])) if final_detections else 0.0,
            'mean_height': float(np.mean([d.get('height', 2.0) for d in final_detections])) if final_detections else 0.0,
            'mean_width': float(np.mean([d.get('width', 0.15) for d in final_detections])) if final_detections else 0.0,
            'mean_distribution_score': float(np.mean([d.get('distribution_score', 0.0) for d in final_detections])) if final_detections else 0.0
        }
    }
    
    metrics_file = output_path / f'c_section_metrics_{ground_method}.json'
    with open(metrics_file, 'w') as f:
        json.dump(metrics, f, indent=2)
    print(f"Saved metrics: {metrics_file}")
    
    print(f"\nAll results saved to: {output_path}")

# Summary
print("\n" + "="*60)
print("C-SECTION PILE DETECTION SUMMARY")
print("="*60)
print(f"Site: {site_name}")
print(f"Ground method: {ground_method}")
print(f"Detection method: {detection_method_used}")
print(f"PyTorch available: {TORCH_AVAILABLE}")
print(f"Input points: {len(points):,}")
print(f"Geometric detections: {len(geometric_detections)}")
if TORCH_AVAILABLE:
    print(f"PointNet model trained: {'Yes' if pointnet_results.get('model') else 'No'}")
print(f"Final detections: {len(final_detections)}")
print(f"Success rate: {'GOOD' if len(final_detections) > 0 else 'NO DETECTIONS'}")
print("="*60)
print("\nImplementation Summary:")
print("- Clean PointNet implementation for C-section detection")
print("- Synthetic data generation for training")
print("- Complete training pipeline demonstrated")
print("- Geometric baseline for comparison")
print("- Ready for real data training")
print("\nNext steps:")
print("- Replace synthetic data with real labeled data")
print("- Implement inference on point cloud patches")
print("- Add data augmentation")
print("- Optimize hyperparameters")