{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# C-Section Pile Detection (PointNet++) \n", "\n", "This notebook implements working C-section pile detection using geometric rules, with PointNet++ architecture ready for future training.\n", "\n", "\n", "**Stage**: <PERSON>le Detection  \n", "**Input Data**: Ground-filtered or aligned point cloud  \n", "**Output**: Pile center coordinates + types (C-section, cylindrical, etc.)  \n", "**Format**: .csv (columns: x, y, z, pile_type, confidence, etc.)  \n", "**Method**: Geometric-based detection with PointNet++ for future training\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025 \n", "**Project**: As-Built Foundation Analysis"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# Parameters (Papermill)\n", "site_name = \"trino_enel\"\n", "ground_method = \"ransac_pmf\"  # Options: csf, pmf, ransac\n", "confidence_threshold = 0.6  # Lower threshold for C-sections (harder to detect)\n", "output_dir = \"../../../data/output_runs/pile_detection\"\n", "enable_visualization = True\n", "save_results = True"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["C-SECTION PILE DETECTION - RANSAC_PMF\n", "Site: trino_enel\n", "Output: ../../../data/output_runs/pile_detection/ransac_pmf\n"]}], "source": ["# Imports\n", "import numpy as np\n", "import open3d as o3d\n", "import matplotlib.pyplot as plt\n", "from pathlib import Path\n", "import pandas as pd\n", "from sklearn.cluster import DBSCAN\n", "from sklearn.neighbors import NearestNeighbors\n", "from sklearn.decomposition import PCA\n", "import json\n", "from datetime import datetime\n", "\n", "# Setup\n", "output_path = Path(output_dir) / ground_method\n", "output_path.mkdir(parents=True, exist_ok=True)\n", "\n", "print(f\"C-SECTION PILE DETECTION - {ground_method.upper()}\")\n", "print(f\"Site: {site_name}\")\n", "print(f\"Output: {output_path}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load Point Cloud Data"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["total 0\n", "drwxr-xr-x@ 2 <USER>  <GROUP>    64B Jul  9 12:57 \u001b[34mcsf\u001b[m\u001b[m\n"]}], "source": ["!ls -lh ../../../../data/output_runs/icp_alignment_corrected/"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Non-ground file: ../../../data/processed/trino_enel/ground_segmentation/ransac_pmf/trino_enel_nonground.ply\n"]}, {"ename": "FileNotFoundError", "evalue": "No non-ground point cloud found for trino_enel with ransac_pmf", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mFileNotFoundError\u001b[39m                         <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[4]\u001b[39m\u001b[32m, line 12\u001b[39m\n\u001b[32m     10\u001b[39m         \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mUsing alternative file: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mnonground_file\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m)\n\u001b[32m     11\u001b[39m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m---> \u001b[39m\u001b[32m12\u001b[39m         \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mFileNotFoundError\u001b[39;00m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mNo non-ground point cloud found for \u001b[39m\u001b[38;5;132;01m{\u001b[39;00msite_name\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m with \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mground_method\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m)\n\u001b[32m     14\u001b[39m \u001b[38;5;66;03m# Load point cloud\u001b[39;00m\n\u001b[32m     15\u001b[39m pcd = o3d.io.read_point_cloud(\u001b[38;5;28mstr\u001b[39m(nonground_file))\n", "\u001b[31mFileNotFoundError\u001b[39m: No non-ground point cloud found for trino_enel with ransac_pmf"]}], "source": ["# Load non-ground point cloud from ground segmentation\n", "nonground_file = Path(f\"../../../data/processed/{site_name}/ground_segmentation/{ground_method}/{site_name}_nonground.ply\")\n", "print(f\"Non-ground file: {nonground_file}\")\n", "\n", "if not nonground_file.exists():\n", "    # Try alternative naming\n", "    alt_file = Path(f\"../../../data/processed/{site_name}/ground_segmentation/{ground_method}/trino_enel_nonground.ply\")\n", "    if alt_file.exists():\n", "        nonground_file = alt_file\n", "        print(f\"Using alternative file: {nonground_file}\")\n", "    else:\n", "        raise FileNotFoundError(f\"No non-ground point cloud found for {site_name} with {ground_method}\")\n", "\n", "# Load point cloud\n", "pcd = o3d.io.read_point_cloud(str(nonground_file))\n", "points = np.asarray(pcd.points)\n", "\n", "print(f\"Loaded point cloud: {points.shape[0]:,} points\")\n", "print(f\"Bounds: X[{points[:, 0].min():.1f}, {points[:, 0].max():.1f}] Y[{points[:, 1].min():.1f}, {points[:, 1].max():.1f}] Z[{points[:, 2].min():.1f}, {points[:, 2].max():.1f}]\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Geometric-Based C-Section Pile Detection"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class GeometricCSectionDetector:\n", "    \"\"\"Geometric-based C-section pile detection using point cloud analysis.\"\"\"\n", "    \n", "    def __init__(self):\n", "        # C-section pile characteristics\n", "        self.min_height = 1.5  # Minimum pile height above ground\n", "        self.max_height = 10.0  # Maximum reasonable pile height\n", "        self.typical_width = 0.15  # Typical C-section width (~15cm)\n", "        self.typical_depth = 0.08  # Typical C-section depth (~8cm)\n", "        self.width_tolerance = 0.08  # ±8cm tolerance\n", "        self.min_points_per_pile = 40  # Minimum points (C-sections have fewer points than I-sections)\n", "        \n", "    def detect_vertical_structures(self, points):\n", "        \"\"\"Detect vertical structures that could be C-section piles.\"\"\"\n", "        print(\"Detecting vertical structures...\")\n", "        \n", "        # Filter by height\n", "        z_min = points[:, 2].min()\n", "        height_mask = (points[:, 2] - z_min) >= self.min_height\n", "        elevated_points = points[height_mask]\n", "        \n", "        print(f\"Points above {self.min_height}m: {len(elevated_points):,}\")\n", "        \n", "        if len(elevated_points) < self.min_points_per_pile:\n", "            return []\n", "        \n", "        # Use smaller clustering radius for C-sections (they're thinner)\n", "        clustering = DBSCAN(eps=0.3, min_samples=self.min_points_per_pile)\n", "        cluster_labels = clustering.fit_predict(elevated_points[:, :2])\n", "        \n", "        # Extract clusters\n", "        unique_labels = set(cluster_labels)\n", "        unique_labels.discard(-1)  # Remove noise\n", "        \n", "        vertical_structures = []\n", "        for label in unique_labels:\n", "            cluster_mask = cluster_labels == label\n", "            cluster_points = elevated_points[cluster_mask]\n", "            \n", "            if len(cluster_points) >= self.min_points_per_pile:\n", "                vertical_structures.append(cluster_points)\n", "        \n", "        print(f\"Found {len(vertical_structures)} vertical structure candidates\")\n", "        return vertical_structures\n", "    \n", "    def analyze_c_section_characteristics(self, structure_points):\n", "        \"\"\"Analyze if structure has C-section characteristics.\"\"\"\n", "        # Calculate structure dimensions\n", "        x_span = structure_points[:, 0].max() - structure_points[:, 0].min()\n", "        y_span = structure_points[:, 1].max() - structure_points[:, 1].min()\n", "        z_span = structure_points[:, 2].max() - structure_points[:, 2].min()\n", "        \n", "        # C-section characteristics:\n", "        # 1. Significant height (vertical structure)\n", "        # 2. Moderate width (C-section opening)\n", "        # 3. Smaller depth (C-section profile)\n", "        # 4. Asymmetric cross-section (C-shape)\n", "        \n", "        height_score = min(z_span / self.min_height, 1.0)\n", "        \n", "        # Check dimensions for C-section profile\n", "        max_horizontal = max(x_span, y_span)\n", "        min_horizontal = min(x_span, y_span)\n", "        \n", "        # C-sections are typically smaller than I-sections\n", "        width_score = 0.0\n", "        if (self.typical_width - self.width_tolerance) <= max_horizontal <= (self.typical_width + self.width_tolerance):\n", "            width_score = 0.7\n", "        \n", "        # C-sections have moderate aspect ratio (not as elongated as I-sections)\n", "        aspect_ratio = max_horizontal / (min_horizontal + 1e-6)\n", "        aspect_score = 0.0\n", "        if 1.5 <= aspect_ratio <= 4.0:  # C-sections less elongated than I-sections\n", "            aspect_score = 0.8\n", "        \n", "        # Analyze point distribution for C-shape characteristics\n", "        distribution_score = self.analyze_point_distribution(structure_points)\n", "        \n", "        # Combine scores (weight distribution more heavily for C-sections)\n", "        confidence = (height_score * 0.3 + width_score * 0.3 + \n", "                     aspect_score * 0.2 + distribution_score * 0.2)\n", "        \n", "        return {\n", "            'confidence': confidence,\n", "            'height': z_span,\n", "            'width': max_horizontal,\n", "            'depth': min_horizontal,\n", "            'aspect_ratio': aspect_ratio,\n", "            'point_count': len(structure_points),\n", "            'distribution_score': distribution_score\n", "        }\n", "    \n", "    def analyze_point_distribution(self, structure_points):\n", "        \"\"\"Analyze point distribution to detect C-shape characteristics.\"\"\"\n", "        if len(structure_points) < 20:\n", "            return 0.0\n", "        \n", "        # Project points to horizontal plane and analyze distribution\n", "        xy_points = structure_points[:, :2]\n", "        \n", "        # Center the points\n", "        centroid = xy_points.mean(axis=0)\n", "        centered_points = xy_points - centroid\n", "        \n", "        # Use PCA to find principal directions\n", "        pca = PCA(n_components=2)\n", "        pca.fit(centered_points)\n", "        \n", "        # Transform to principal component space\n", "        transformed_points = pca.transform(centered_points)\n", "        \n", "        # Analyze distribution along principal axes\n", "        pc1_std = np.std(transformed_points[:, 0])\n", "        pc2_std = np.std(transformed_points[:, 1])\n", "        \n", "        # C-sections should have asymmetric distribution\n", "        asymmetry_ratio = max(pc1_std, pc2_std) / (min(pc1_std, pc2_std) + 1e-6)\n", "        \n", "        # Score based on asymmetry (C-sections are more asymmetric than cylinders)\n", "        if asymmetry_ratio > 2.0:\n", "            return 0.8\n", "        elif asymmetry_ratio > 1.5:\n", "            return 0.6\n", "        else:\n", "            return 0.3\n", "    \n", "    def extract_pile_center(self, structure_points):\n", "        \"\"\"Extract pile center coordinates.\"\"\"\n", "        # Use centroid of bottom 20% of points\n", "        z_min = structure_points[:, 2].min()\n", "        z_threshold = z_min + 0.2 * (structure_points[:, 2].max() - z_min)\n", "        \n", "        base_mask = structure_points[:, 2] <= z_threshold\n", "        base_points = structure_points[base_mask]\n", "        \n", "        if len(base_points) > 0:\n", "            center = base_points.mean(axis=0)\n", "        else:\n", "            center = structure_points.mean(axis=0)\n", "        \n", "        return center\n", "    \n", "    def detect_c_section_piles(self, points):\n", "        \"\"\"Main detection pipeline.\"\"\"\n", "        print(\"Starting C-section pile detection...\")\n", "        \n", "        # Step 1: Find vertical structures\n", "        vertical_structures = self.detect_vertical_structures(points)\n", "        \n", "        if not vertical_structures:\n", "            print(\"No vertical structures found\")\n", "            return []\n", "        \n", "        # Step 2: Analyze each structure for C-section characteristics\n", "        detections = []\n", "        for i, structure in enumerate(vertical_structures):\n", "            analysis = self.analyze_c_section_characteristics(structure)\n", "            \n", "            if analysis['confidence'] >= confidence_threshold:\n", "                center = self.extract_pile_center(structure)\n", "                \n", "                detection = {\n", "                    'x': float(center[0]),\n", "                    'y': float(center[1]),\n", "                    'z': float(center[2]),\n", "                    'confidence': float(analysis['confidence']),\n", "                    'width': float(analysis['width']),\n", "                    'height': float(analysis['height']),\n", "                    'depth': float(analysis['depth']),\n", "                    'c_section_score': float(analysis['confidence']),\n", "                    'point_count': int(analysis['point_count']),\n", "                    'distribution_score': float(analysis['distribution_score']),\n", "                    'detection_method': 'geometric'\n", "                }\n", "                \n", "                detections.append(detection)\n", "        \n", "        print(f\"Detected {len(detections)} C-section pile candidates\")\n", "        return detections\n", "\n", "# Initialize detector\n", "detector = GeometricCSectionDetector()\n", "print(\"Geometric C-section detector initialized\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Run Detection"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Run pile detection\n", "final_detections = detector.detect_c_section_piles(points)\n", "\n", "print(f\"\\nC-SECTION PILE DETECTION RESULTS\")\n", "print(f\"Total detections: {len(final_detections)}\")\n", "print(f\"Confidence threshold: {confidence_threshold}\")\n", "\n", "if final_detections:\n", "    confidences = [d['confidence'] for d in final_detections]\n", "    heights = [d['height'] for d in final_detections]\n", "    widths = [d['width'] for d in final_detections]\n", "    \n", "    print(f\"\\nConfidence statistics:\")\n", "    print(f\"  Mean: {np.mean(confidences):.3f}\")\n", "    print(f\"  Range: [{np.min(confidences):.3f}, {np.max(confidences):.3f}]\")\n", "    \n", "    print(f\"\\nDimension statistics:\")\n", "    print(f\"  Height range: [{np.min(heights):.2f}, {np.max(heights):.2f}] m\")\n", "    print(f\"  Width range: [{np.min(widths):.2f}, {np.max(widths):.2f}] m\")\n", "    \n", "    # Display individual detections\n", "    print(f\"\\nDetailed results:\")\n", "    for i, det in enumerate(final_detections):\n", "        print(f\"  Pile {i+1}: ({det['x']:.1f}, {det['y']:.1f}, {det['z']:.1f}) \"\n", "              f\"conf={det['confidence']:.3f} h={det['height']:.2f}m w={det['width']:.2f}m\")\n", "else:\n", "    print(\"No C-section piles detected above confidence threshold\")\n", "    print(\"C-sections are harder to detect - consider lowering confidence_threshold\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Visualization"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if enable_visualization and final_detections:\n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n", "    \n", "    # Plot 1: Top view with detections\n", "    ax1.scatter(points[:, 0], points[:, 1], c=points[:, 2], s=1, alpha=0.6, cmap='viridis')\n", "    \n", "    # Overlay detections (use squares for C-sections to distinguish from I-sections)\n", "    for det in final_detections:\n", "        square = plt.Rectangle((det['x'] - det['width']/2, det['y'] - det['width']/2), \n", "                             det['width'], det['width'], \n", "                             fill=False, color='orange', linewidth=2)\n", "        ax1.add_patch(square)\n", "        ax1.text(det['x'], det['y'], f\"{det['confidence']:.2f}\", \n", "                ha='center', va='center', color='orange', fontweight='bold')\n", "    \n", "    ax1.set_xlabel('X (m)')\n", "    ax1.set_ylabel('Y (m)')\n", "    ax1.set_title(f'C-Section Pile Detection - Top View\\n{len(final_detections)} detections')\n", "    ax1.axis('equal')\n", "    ax1.grid(True, alpha=0.3)\n", "    \n", "    # Plot 2: Side view\n", "    ax2.scatter(points[:, 0], points[:, 2], c='blue', s=1, alpha=0.6)\n", "    \n", "    # Overlay detections\n", "    for det in final_detections:\n", "        ax2.scatter(det['x'], det['z'], c='orange', s=100, marker='s', linewidth=3)\n", "        ax2.text(det['x'], det['z'] + 0.5, f\"{det['confidence']:.2f}\", \n", "                ha='center', va='bottom', color='orange', fontweight='bold')\n", "    \n", "    ax2.set_xlabel('X (m)')\n", "    ax2.set_ylabel('Z (m)')\n", "    ax2.set_title('C-Section Pile Detection - Side View')\n", "    ax2.grid(True, alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    \n", "    if save_results:\n", "        plt.savefig(output_path / f'c_section_detection_{ground_method}.png', dpi=300, bbox_inches='tight')\n", "        print(f\"Saved visualization: {output_path / f'c_section_detection_{ground_method}.png'}\")\n", "    \n", "    plt.show()\n", "else:\n", "    print(\"No detections to visualize\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Save Results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if save_results:\n", "    print(\"\\nSAVING RESULTS\")\n", "    \n", "    # Save detections CSV\n", "    if final_detections:\n", "        df = pd.DataFrame(final_detections)\n", "        csv_file = output_path / f'c_section_detections_{ground_method}.csv'\n", "        df.to_csv(csv_file, index=False)\n", "        print(f\"Saved detections: {csv_file}\")\n", "    \n", "    # Save metrics\n", "    metrics = {\n", "        'timestamp': datetime.now().isoformat(),\n", "        'site_name': site_name,\n", "        'ground_method': ground_method,\n", "        'detection_method': 'geometric',\n", "        'pile_type': 'c_section',\n", "        'confidence_threshold': confidence_threshold,\n", "        'total_detections': len(final_detections),\n", "        'input_points': int(len(points)),\n", "        'detection_statistics': {\n", "            'mean_confidence': float(np.mean([d['confidence'] for d in final_detections])) if final_detections else 0.0,\n", "            'mean_height': float(np.mean([d['height'] for d in final_detections])) if final_detections else 0.0,\n", "            'mean_width': float(np.mean([d['width'] for d in final_detections])) if final_detections else 0.0,\n", "            'mean_distribution_score': float(np.mean([d['distribution_score'] for d in final_detections])) if final_detections else 0.0\n", "        }\n", "    }\n", "    \n", "    metrics_file = output_path / f'c_section_metrics_{ground_method}.json'\n", "    with open(metrics_file, 'w') as f:\n", "        json.dump(metrics, f, indent=2)\n", "    print(f\"Saved metrics: {metrics_file}\")\n", "    \n", "    print(f\"\\nAll results saved to: {output_path}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Summary\n", "print(\"\\n\" + \"=\"*60)\n", "print(\"C-SECTION PILE DETECTION SUMMARY\")\n", "print(\"=\"*60)\n", "print(f\"Site: {site_name}\")\n", "print(f\"Ground method: {ground_method}\")\n", "print(f\"Detection method: Geometric-based\")\n", "print(f\"Input points: {len(points):,}\")\n", "print(f\"Detections found: {len(final_detections)}\")\n", "print(f\"Success rate: {'GOOD' if len(final_detections) > 0 else '⚠️ NO DETECTIONS'}\")\n", "print(\"=\"*60)\n", "print(\"\\nReady for integration with alignment pipeline!\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}