# Parameters (Papermill)
site_name = "trino_enel"
ground_method = "csf"  # Options: csf, pmf, ransac
confidence_threshold = 0.6  # Lower threshold for C-sections (harder to detect)
output_dir = "../../../../../data/output_runs/pile_detection"
use_aligned_data = True  # Use ICP aligned point clouds if available
enable_visualization = True
save_results = True

# Imports
import numpy as np
import open3d as o3d
import matplotlib.pyplot as plt
from pathlib import Path
import pandas as pd
from sklearn.cluster import DBSCAN
from sklearn.neighbors import NearestNeighbors
from sklearn.decomposition import PCA
import json
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Deep learning imports
try:
    import torch
    import torch.nn as nn
    import torch.nn.functional as F
    from torch.utils.data import Dataset, DataLoader
    TORCH_AVAILABLE = True
    print(f"PyTorch version: {torch.__version__}")
    print(f"CUDA available: {torch.cuda.is_available()}")
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
except ImportError:
    TORCH_AVAILABLE = False
    print("PyTorch not available - using geometric detection only")
    device = None

# Setup
output_path = Path(output_dir) / ground_method
output_path.mkdir(parents=True, exist_ok=True)

print(f"C-SECTION PILE DETECTION - {ground_method.upper()}")
print(f"Site: {site_name}")
print(f"Output: {output_path}")
print(f"Use aligned data: {'Enabled' if use_aligned_data else '❌ Disabled'}")
print(f"Confidence threshold: {confidence_threshold}")


!ls - lh ../../../../../data

from pathlib import Path
import numpy as np
import open3d as o3d

# Load point cloud data, preferring aligned ICP output if available
print("==> LOADING POINT CLOUD DATA")

aligned_file = Path(f"../../../../../data/output_runs/icp_alignment_corrected/{ground_method}/aligned_ifc_{ground_method}.ply")
if aligned_file.exists():
    input_file = aligned_file
    data_source = "ICP aligned"
    print(f"Using aligned point cloud: {input_file}")
else:
    print(f"Aligned file not found: {aligned_file}")
    print("Falling back to ground segmentation data")
    input_file = Path(f"../../../../../data/ground_segmentation/{ground_method}/ifc_ground.ply")
    data_source = "Ground segmentation"

# Load point cloud
pcd = o3d.io.read_point_cloud(str(input_file))
points = np.asarray(pcd.points)

# Display summary
print(f"\nDATA SOURCE: {data_source}")
print(f"Points loaded: {points.shape[0]:,}")
print(f"Bounds: X[{points[:, 0].min():.1f}, {points[:, 0].max():.1f}] "
      f"Y[{points[:, 1].min():.1f}, {points[:, 1].max():.1f}] "
      f"Z[{points[:, 2].min():.1f}, {points[:, 2].max():.1f}]")


if TORCH_AVAILABLE:
    # Simple PointNet++ for C-section detection
    class SimplePointNet(nn.Module):
        """Simplified PointNet for C-section pile detection"""
        def __init__(self, num_points=1024, num_classes=2):
            super(SimplePointNet, self).__init__()
            self.num_points = num_points
            
            # Point feature extraction
            self.conv1 = nn.Conv1d(3, 64, 1)
            self.conv2 = nn.Conv1d(64, 128, 1)
            self.conv3 = nn.Conv1d(128, 256, 1)
            
            self.bn1 = nn.BatchNorm1d(64)
            self.bn2 = nn.BatchNorm1d(128)
            self.bn3 = nn.BatchNorm1d(256)
            
            # Classification head
            self.fc1 = nn.Linear(256, 128)
            self.fc2 = nn.Linear(128, 64)
            self.fc3 = nn.Linear(64, num_classes)
            
            self.dropout = nn.Dropout(0.3)
            
        def forward(self, x):
            # x: [batch_size, 3, num_points]
            x = F.relu(self.bn1(self.conv1(x)))
            x = F.relu(self.bn2(self.conv2(x)))
            x = F.relu(self.bn3(self.conv3(x)))
            
            # Global max pooling
            x = torch.max(x, 2)[0]  # [batch_size, 256]
            
            # Classification
            x = F.relu(self.fc1(x))
            x = self.dropout(x)
            x = F.relu(self.fc2(x))
            x = self.dropout(x)
            x = self.fc3(x)
            
            return F.log_softmax(x, dim=1)
    
    print("Simple PointNet model implemented")
else:
    print("Skipping PointNet implementation - PyTorch not available")

# Real data utilities
if TORCH_AVAILABLE:
    def load_pile_coordinates(site_name):
        """Load pile coordinates from IFC metadata"""
        pile_file = Path(f"../../../../../data/processed/{site_name}/advanced_ifc_metadata/advanced_pile_coordinates.csv")
        
        if not pile_file.exists():
            print(f"Pile coordinates not found: {pile_file}")
            return None
        
        pile_df = pd.read_csv(pile_file)
        print(f"Loaded {len(pile_df)} pile coordinates from IFC")
        
        # Extract coordinates
        pile_coords = pile_df[['X', 'Y', 'Z']].values
        return pile_coords, pile_df
    
    def create_training_patches(points, pile_coords, patch_size=3.0, num_points=512):
        """Create training patches from real point cloud data"""
        print(f"Creating training patches from real data...")
        print(f"Point cloud: {points.shape[0]:,} points")
        print(f"Pile locations: {len(pile_coords)} piles")
        
        X = []
        y = []
        
        # Create positive samples (patches around pile locations)
        for pile_coord in pile_coords:
            # Extract points within patch_size radius of pile
            distances = np.linalg.norm(points[:, :2] - pile_coord[:2], axis=1)
            patch_mask = distances < patch_size / 2
            
            if np.sum(patch_mask) >= 50:  # Minimum points for a valid patch
                patch_points = points[patch_mask]
                
                # Center the patch
                patch_points = patch_points - patch_points.mean(axis=0)
                
                # Resample to fixed number of points
                if len(patch_points) >= num_points:
                    indices = np.random.choice(len(patch_points), num_points, replace=False)
                else:
                    indices = np.random.choice(len(patch_points), num_points, replace=True)
                
                patch_points = patch_points[indices]
                
                X.append(patch_points)
                y.append(1)  # Pile patch
        
        # Create negative samples (random patches away from piles)
        num_negative = len(X)  # Same number as positive samples
        
        for _ in range(num_negative):
            # Random center point
            x_min, x_max = points[:, 0].min(), points[:, 0].max()
            y_min, y_max = points[:, 1].min(), points[:, 1].max()
            
            center_x = np.random.uniform(x_min, x_max)
            center_y = np.random.uniform(y_min, y_max)
            
            # Check if this center is far enough from any pile
            distances_to_piles = np.linalg.norm(pile_coords[:, :2] - [center_x, center_y], axis=1)
            
            if np.min(distances_to_piles) > patch_size:  # Far from any pile
                # Extract points around this center
                distances = np.linalg.norm(points[:, :2] - [center_x, center_y], axis=1)
                patch_mask = distances < patch_size / 2
                
                if np.sum(patch_mask) >= 50:
                    patch_points = points[patch_mask]
                    
                    # Center the patch
                    patch_points = patch_points - patch_points.mean(axis=0)
                    
                    # Resample to fixed number of points
                    if len(patch_points) >= num_points:
                        indices = np.random.choice(len(patch_points), num_points, replace=False)
                    else:
                        indices = np.random.choice(len(patch_points), num_points, replace=True)
                    
                    patch_points = patch_points[indices]
                    
                    X.append(patch_points)
                    y.append(0)  # Background patch
        
        X = np.array(X)
        y = np.array(y)
        
        print(f"Created {len(X)} training patches:")
        print(f"  Pile patches: {np.sum(y == 1)}")
        print(f"  Background patches: {np.sum(y == 0)}")
        
        return X, y
    
    print("Real data utilities implemented")
else:
    print("Skipping real data utilities - PyTorch not available")

if TORCH_AVAILABLE:
    # Simple dataset class
    class CSectionDataset(Dataset):
        def __init__(self, X, y):
            self.X = torch.FloatTensor(X)
            self.y = torch.LongTensor(y)
        
        def __len__(self):
            return len(self.X)
        
        def __getitem__(self, idx):
            # Return [3, N] format for PointNet
            return self.X[idx].transpose(0, 1), self.y[idx]
    
    # Training function
    def train_model(model, train_loader, num_epochs=10):
        """Train the PointNet model"""
        optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
        criterion = nn.NLLLoss()
        
        model.train()
        for epoch in range(num_epochs):
            total_loss = 0
            correct = 0
            total = 0
            
            for batch_idx, (data, target) in enumerate(train_loader):
                data, target = data.to(device), target.to(device)
                
                optimizer.zero_grad()
                output = model(data)
                loss = criterion(output, target)
                loss.backward()
                optimizer.step()
                
                total_loss += loss.item()
                pred = output.argmax(dim=1)
                correct += pred.eq(target).sum().item()
                total += target.size(0)
            
            accuracy = 100. * correct / total
            avg_loss = total_loss / len(train_loader)
            print(f'Epoch {epoch+1}/{num_epochs}: Loss: {avg_loss:.4f}, Accuracy: {accuracy:.2f}%')
        
        return model
    
    print("Training utilities ready")
else:
    print("Skipping training setup - PyTorch not available")

class GeometricCSectionDetector:
    """Geometric-based C-section pile detection using point cloud analysis."""
    
    def __init__(self):
        # C-section pile characteristics
        self.min_height = 1.5  # Minimum pile height above ground
        self.max_height = 10.0  # Maximum reasonable pile height
        self.typical_width = 0.15  # Typical C-section width (~15cm)
        self.typical_depth = 0.08  # Typical C-section depth (~8cm)
        self.width_tolerance = 0.08  # ±8cm tolerance
        self.min_points_per_pile = 40  # Minimum points (C-sections have fewer points than I-sections)
        
    def detect_vertical_structures(self, points):
        """Detect vertical structures that could be C-section piles."""
        print("Detecting vertical structures...")
        
        # Filter by height
        z_min = points[:, 2].min()
        height_mask = (points[:, 2] - z_min) >= self.min_height
        elevated_points = points[height_mask]
        
        print(f"Points above {self.min_height}m: {len(elevated_points):,}")
        
        if len(elevated_points) < self.min_points_per_pile:
            return []
        
        # Use smaller clustering radius for C-sections (they're thinner)
        clustering = DBSCAN(eps=0.3, min_samples=self.min_points_per_pile)
        cluster_labels = clustering.fit_predict(elevated_points[:, :2])
        
        # Extract clusters
        unique_labels = set(cluster_labels)
        unique_labels.discard(-1)  # Remove noise
        
        vertical_structures = []
        for label in unique_labels:
            cluster_mask = cluster_labels == label
            cluster_points = elevated_points[cluster_mask]
            
            if len(cluster_points) >= self.min_points_per_pile:
                vertical_structures.append(cluster_points)
        
        print(f"Found {len(vertical_structures)} vertical structure candidates")
        return vertical_structures
    
    def analyze_c_section_characteristics(self, structure_points):
        """Analyze if structure has C-section characteristics."""
        # Calculate structure dimensions
        x_span = structure_points[:, 0].max() - structure_points[:, 0].min()
        y_span = structure_points[:, 1].max() - structure_points[:, 1].min()
        z_span = structure_points[:, 2].max() - structure_points[:, 2].min()
        
        # C-section characteristics:
        # 1. Significant height (vertical structure)
        # 2. Moderate width (C-section opening)
        # 3. Smaller depth (C-section profile)
        # 4. Asymmetric cross-section (C-shape)
        
        height_score = min(z_span / self.min_height, 1.0)
        
        # Check dimensions for C-section profile
        max_horizontal = max(x_span, y_span)
        min_horizontal = min(x_span, y_span)
        
        # C-sections are typically smaller than I-sections
        width_score = 0.0
        if (self.typical_width - self.width_tolerance) <= max_horizontal <= (self.typical_width + self.width_tolerance):
            width_score = 0.7
        
        # C-sections have moderate aspect ratio (not as elongated as I-sections)
        aspect_ratio = max_horizontal / (min_horizontal + 1e-6)
        aspect_score = 0.0
        if 1.5 <= aspect_ratio <= 4.0:  # C-sections less elongated than I-sections
            aspect_score = 0.8
        
        # Analyze point distribution for C-shape characteristics
        distribution_score = self.analyze_point_distribution(structure_points)
        
        # Combine scores (weight distribution more heavily for C-sections)
        confidence = (height_score * 0.3 + width_score * 0.3 + 
                     aspect_score * 0.2 + distribution_score * 0.2)
        
        return {
            'confidence': confidence,
            'height': z_span,
            'width': max_horizontal,
            'depth': min_horizontal,
            'aspect_ratio': aspect_ratio,
            'point_count': len(structure_points),
            'distribution_score': distribution_score
        }
    
    def analyze_point_distribution(self, structure_points):
        """Analyze point distribution to detect C-shape characteristics."""
        if len(structure_points) < 20:
            return 0.0
        
        # Project points to horizontal plane and analyze distribution
        xy_points = structure_points[:, :2]
        
        # Center the points
        centroid = xy_points.mean(axis=0)
        centered_points = xy_points - centroid
        
        # Use PCA to find principal directions
        pca = PCA(n_components=2)
        pca.fit(centered_points)
        
        # Transform to principal component space
        transformed_points = pca.transform(centered_points)
        
        # Analyze distribution along principal axes
        pc1_std = np.std(transformed_points[:, 0])
        pc2_std = np.std(transformed_points[:, 1])
        
        # C-sections should have asymmetric distribution
        asymmetry_ratio = max(pc1_std, pc2_std) / (min(pc1_std, pc2_std) + 1e-6)
        
        # Score based on asymmetry (C-sections are more asymmetric than cylinders)
        if asymmetry_ratio > 2.0:
            return 0.8
        elif asymmetry_ratio > 1.5:
            return 0.6
        else:
            return 0.3
    
    def extract_pile_center(self, structure_points):
        """Extract pile center coordinates."""
        # Use centroid of bottom 20% of points
        z_min = structure_points[:, 2].min()
        z_threshold = z_min + 0.2 * (structure_points[:, 2].max() - z_min)
        
        base_mask = structure_points[:, 2] <= z_threshold
        base_points = structure_points[base_mask]
        
        if len(base_points) > 0:
            center = base_points.mean(axis=0)
        else:
            center = structure_points.mean(axis=0)
        
        return center
    
    def detect_c_section_piles(self, points):
        """Main detection pipeline."""
        print("Starting C-section pile detection...")
        
        # Step 1: Find vertical structures
        vertical_structures = self.detect_vertical_structures(points)
        
        if not vertical_structures:
            print("No vertical structures found")
            return []
        
        # Step 2: Analyze each structure for C-section characteristics
        detections = []
        for i, structure in enumerate(vertical_structures):
            analysis = self.analyze_c_section_characteristics(structure)
            
            if analysis['confidence'] >= confidence_threshold:
                center = self.extract_pile_center(structure)
                
                detection = {
                    'x': float(center[0]),
                    'y': float(center[1]),
                    'z': float(center[2]),
                    'confidence': float(analysis['confidence']),
                    'width': float(analysis['width']),
                    'height': float(analysis['height']),
                    'depth': float(analysis['depth']),
                    'c_section_score': float(analysis['confidence']),
                    'point_count': int(analysis['point_count']),
                    'distribution_score': float(analysis['distribution_score']),
                    'detection_method': 'geometric'
                }
                
                detections.append(detection)
        
        print(f"Detected {len(detections)} C-section pile candidates")
        return detections

# Initialize detector
detector = GeometricCSectionDetector()
print("Geometric C-section detector initialized")

if TORCH_AVAILABLE:
    print("==> TRAINING POINTNET MODEL ON REAL DATA")
    
    # Load pile coordinates from IFC metadata
    pile_coords, pile_df = load_pile_coordinates(site_name)
    
    if pile_coords is not None:
        print(f"\nPile coordinate statistics:")
        print(f"  X range: [{pile_coords[:, 0].min():.1f}, {pile_coords[:, 0].max():.1f}]")
        print(f"  Y range: [{pile_coords[:, 1].min():.1f}, {pile_coords[:, 1].max():.1f}]")
        print(f"  Z range: [{pile_coords[:, 2].min():.1f}, {pile_coords[:, 2].max():.1f}]")
        
        # Create training patches from real data
        X_train, y_train = create_training_patches(points, pile_coords, patch_size=3.0, num_points=512)
        
        if len(X_train) > 0:
            print(f"\nTraining data shape: {X_train.shape}")
            print(f"Labels shape: {y_train.shape}")
            print(f"Pile samples: {np.sum(y_train == 1)}")
            print(f"Background samples: {np.sum(y_train == 0)}")
            
            # Create dataset and dataloader
            train_dataset = CSectionDataset(X_train, y_train)
            train_loader = DataLoader(train_dataset, batch_size=8, shuffle=True)
            
            # Initialize model
            model = SimplePointNet(num_points=512, num_classes=2)
            model.to(device)
            print(f"\nModel initialized on {device}")
            
            # Train the model
            print("\nStarting training on real data...")
            trained_model = train_model(model, train_loader, num_epochs=10)
            
            print("\nTraining completed!")
            
            # Test on a few samples
            model.eval()
            with torch.no_grad():
                test_samples = min(5, len(X_train))
                test_data = torch.FloatTensor(X_train[:test_samples]).transpose(1, 2).to(device)
                test_labels = y_train[:test_samples]
                
                outputs = model(test_data)
                predictions = outputs.argmax(dim=1).cpu().numpy()
                probabilities = torch.exp(outputs).cpu().numpy()
                
                print(f"\nTest Results on Real Data:")
                for i in range(test_samples):
                    label_name = 'Pile' if test_labels[i] == 1 else 'Background'
                    pred_name = 'Pile' if predictions[i] == 1 else 'Background'
                    confidence = probabilities[i][predictions[i]]
                    print(f"  Sample {i+1}: True={label_name}, Predicted={pred_name} (conf: {confidence:.3f})")
            
            # Store results for later use
            pointnet_results = {
                'model': trained_model,
                'training_data_size': len(X_train),
                'pile_coordinates': pile_coords,
                'detections': []  # Would be filled during inference
            }
            
        else:
            print("No training patches could be created")
            pointnet_results = {'detections': [], 'model': None}
    else:
        print("No pile coordinates available for training")
        pointnet_results = {'detections': [], 'model': None}
    
else:
    print("Skipping PointNet training - PyTorch not available")
    pointnet_results = {'detections': [], 'model': None}

if TORCH_AVAILABLE and pointnet_results.get('model') is not None:
    print("==> POINTNET INFERENCE ON REAL DATA")
    
    model = pointnet_results['model']
    model.eval()
    
    # Generate patches for inference across the entire point cloud
    def generate_inference_patches(points, patch_size=3.0, stride=2.0, num_points=512):
        """Generate patches for inference across the point cloud"""
        x_min, x_max = points[:, 0].min(), points[:, 0].max()
        y_min, y_max = points[:, 1].min(), points[:, 1].max()
        
        patches = []
        centers = []
        
        x = x_min
        while x < x_max - patch_size:
            y = y_min
            while y < y_max - patch_size:
                center_x = x + patch_size / 2
                center_y = y + patch_size / 2
                
                # Extract points in this patch
                distances = np.linalg.norm(points[:, :2] - [center_x, center_y], axis=1)
                patch_mask = distances < patch_size / 2
                
                if np.sum(patch_mask) >= 50:  # Minimum points
                    patch_points = points[patch_mask]
                    
                    # Center the patch
                    patch_points = patch_points - patch_points.mean(axis=0)
                    
                    # Resample to fixed number of points
                    if len(patch_points) >= num_points:
                        indices = np.random.choice(len(patch_points), num_points, replace=False)
                    else:
                        indices = np.random.choice(len(patch_points), num_points, replace=True)
                    
                    patch_points = patch_points[indices]
                    patches.append(patch_points)
                    centers.append([center_x, center_y])
                
                y += stride
            x += stride
        
        return np.array(patches), np.array(centers)
    
    # DEBUGGING: Check point cloud size first
    x_min, x_max = points[:, 0].min(), points[:, 0].max()
    y_min, y_max = points[:, 1].min(), points[:, 1].max()
    area = (x_max - x_min) * (y_max - y_min)
    
    print(f"Point cloud bounds: X[{x_min:.1f}, {x_max:.1f}] Y[{y_min:.1f}, {y_max:.1f}]")
    print(f"Area: {area:.1f} m²")
    
    # Estimate patches with current settings
    patch_size, stride = 3.0, 2.0
    x_patches = int((x_max - x_min - patch_size) / stride) + 1
    y_patches = int((y_max - y_min - patch_size) / stride) + 1
    estimated_patches = x_patches * y_patches
    
    print(f"Estimated patches: {estimated_patches} ({x_patches} x {y_patches})")
    
    if estimated_patches > 500:
        print("WARNING: Too many patches! Using reduced settings...")
        patch_size, stride = 5.0, 4.0  # Larger patches, bigger stride
        x_patches = int((x_max - x_min - patch_size) / stride) + 1
        y_patches = int((y_max - y_min - patch_size) / stride) + 1
        estimated_patches = x_patches * y_patches
        print(f"Reduced to: {estimated_patches} patches")
    
    # Generate inference patches with optimized settings
    inference_patches, patch_centers = generate_inference_patches(points, patch_size=patch_size, stride=stride)
    
    print(f"Actually generated {len(inference_patches)} patches for inference")
    
    if len(inference_patches) > 0:
        # Run inference
        pile_scores = []
        batch_size = 16
        
        with torch.no_grad():
            for i in range(0, len(inference_patches), batch_size):
                batch_patches = inference_patches[i:i+batch_size]
                batch_tensor = torch.FloatTensor(batch_patches).transpose(1, 2).to(device)
                
                outputs = model(batch_tensor)
                probabilities = torch.exp(outputs).cpu().numpy()
                
                # Get pile probabilities (class 1)
                batch_pile_scores = probabilities[:, 1]
                pile_scores.extend(batch_pile_scores)
                
                # More frequent logging
                if i % (batch_size * 2) == 0:  # Log every 32 patches instead of 160
                    print(f"  Processed {i}/{len(inference_patches)} patches ({100*i/len(inference_patches):.1f}%)")
        
        pile_scores = np.array(pile_scores)
        
        # Filter high-confidence detections
        confidence_threshold = 0.7
        high_conf_mask = pile_scores > confidence_threshold
        
        print(f"\nPointNet Inference Results:")
        print(f"  Total patches: {len(inference_patches)}")
        print(f"  High confidence patches: {np.sum(high_conf_mask)}")
        print(f"  Mean pile score: {np.mean(pile_scores):.3f}")
        print(f"  Max pile score: {np.max(pile_scores):.3f}")
        
        # Convert to pile detections
        pointnet_detections = []
        if np.sum(high_conf_mask) > 0:
            high_conf_centers = patch_centers[high_conf_mask]
            high_conf_scores = pile_scores[high_conf_mask]
            
            for center, score in zip(high_conf_centers, high_conf_scores):
                # Estimate Z coordinate
                nearby_mask = np.linalg.norm(points[:, :2] - center, axis=1) < 1.0
                if np.sum(nearby_mask) > 0:
                    z_coord = points[nearby_mask, 2].mean()
                else:
                    z_coord = points[:, 2].mean()
                
                detection = {
                    'x': float(center[0]),
                    'y': float(center[1]),
                    'z': float(z_coord),
                    'confidence': float(score),
                    'detection_method': 'pointnet_trained'
                }
                pointnet_detections.append(detection)
        
        pointnet_results['detections'] = pointnet_detections
        print(f"  Final PointNet detections: {len(pointnet_detections)}")
    
else:
    print("Skipping PointNet inference - no trained model available")
    pointnet_detections = []

# Run geometric detection
geometric_detections = detector.detect_c_section_piles(points)

print(f"\n==> DETECTION RESULTS COMPARISON")
print(f"\nGeometric Method:")
print(f"  Total detections: {len(geometric_detections)}")
print(f"  Confidence threshold: {confidence_threshold}")

if TORCH_AVAILABLE:
    print(f"\nPointNet Method:")
    print(f"  Model trained: {'Yes' if pointnet_results.get('model') else 'No'}")
    print(f"  Ready for inference: {'Yes' if pointnet_results.get('model') else 'No'}")

# For this demo, use geometric detections as the primary method
# In practice, you would use the trained PointNet model for inference
if len(geometric_detections) > 0:
    final_detections = geometric_detections
    detection_method_used = 'geometric'
    print(f"\nUsing geometric detections for final results")
    if TORCH_AVAILABLE and pointnet_results.get('model'):
        print(f"Note: Trained PointNet model is available for future inference")
else:
    final_detections = []
    detection_method_used = 'none'
    print(f"\nNo detections found")
    if TORCH_AVAILABLE and pointnet_results.get('model'):
        print(f"Note: Trained PointNet model is available for inference on real data")

if final_detections:
    confidences = [d['confidence'] for d in final_detections]
    heights = [d.get('height', 2.0) for d in final_detections]
    widths = [d.get('width', 0.15) for d in final_detections]
    
    print(f"\nFinal Detection Statistics ({detection_method_used}):")
    print(f"  Count: {len(final_detections)}")
    print(f"  Mean confidence: {np.mean(confidences):.3f}")
    print(f"  Confidence range: [{np.min(confidences):.3f}, {np.max(confidences):.3f}]")
    print(f"  Height range: [{np.min(heights):.2f}, {np.max(heights):.2f}] m")
    print(f"  Width range: [{np.min(widths):.2f}, {np.max(widths):.2f}] m")
    
    # Display individual detections
    print(f"\nDetailed Results:")
    for i, det in enumerate(final_detections[:10]):  # Show first 10
        print(f"  Pile {i+1}: ({det['x']:.1f}, {det['y']:.1f}, {det['z']:.1f}) "
              f"conf={det['confidence']:.3f} method={det.get('detection_method', 'unknown')}")
    if len(final_detections) > 10:
        print(f"  ... and {len(final_detections) - 10} more")
else:
    print("\nNo C-section piles detected with either method")
    print("Consider:")
    print("  - Lowering confidence threshold")
    print("  - Adjusting patch size for PointNet++")
    print("  - Training PointNet++ model on labeled data")

if enable_visualization and final_detections:
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # Plot 1: Top view with detections
    ax1.scatter(points[:, 0], points[:, 1], c=points[:, 2], s=1, alpha=0.6, cmap='viridis')
    
    # Overlay detections (use squares for C-sections to distinguish from I-sections)
    for det in final_detections:
        square = plt.Rectangle((det['x'] - det['width']/2, det['y'] - det['width']/2), 
                             det['width'], det['width'], 
                             fill=False, color='orange', linewidth=2)
        ax1.add_patch(square)
        ax1.text(det['x'], det['y'], f"{det['confidence']:.2f}", 
                ha='center', va='center', color='orange', fontweight='bold')
    
    ax1.set_xlabel('X (m)')
    ax1.set_ylabel('Y (m)')
    ax1.set_title(f'C-Section Pile Detection - Top View\n{len(final_detections)} detections')
    ax1.axis('equal')
    ax1.grid(True, alpha=0.3)
    
    # Plot 2: Side view
    ax2.scatter(points[:, 0], points[:, 2], c='blue', s=1, alpha=0.6)
    
    # Overlay detections
    for det in final_detections:
        ax2.scatter(det['x'], det['z'], c='orange', s=100, marker='s', linewidth=3)
        ax2.text(det['x'], det['z'] + 0.5, f"{det['confidence']:.2f}", 
                ha='center', va='bottom', color='orange', fontweight='bold')
    
    ax2.set_xlabel('X (m)')
    ax2.set_ylabel('Z (m)')
    ax2.set_title('C-Section Pile Detection - Side View')
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    if save_results:
        plt.savefig(output_path / f'c_section_detection_{ground_method}.png', dpi=300, bbox_inches='tight')
        print(f"Saved visualization: {output_path / f'c_section_detection_{ground_method}.png'}")
    
    plt.show()
else:
    print("No detections to visualize")

if save_results:
    print("\nSAVING RESULTS")
    
    # Save detections CSV
    if final_detections:
        df = pd.DataFrame(final_detections)
        csv_file = output_path / f'c_section_detections_{ground_method}.csv'
        df.to_csv(csv_file, index=False)
        print(f"Saved detections: {csv_file}")
    
    # Save metrics
    metrics = {
        'timestamp': datetime.now().isoformat(),
        'site_name': site_name,
        'ground_method': ground_method,
        'detection_method_used': detection_method_used,
        'pytorch_available': TORCH_AVAILABLE,
        'pile_type': 'c_section',
        'confidence_threshold': confidence_threshold,
        'total_detections': len(final_detections),
        'input_points': int(len(points)),
        'data_source': data_source,
        'use_aligned_data': use_aligned_data,
        'input_file': str(input_file),
        'detection_statistics': {
            'mean_confidence': float(np.mean([d['confidence'] for d in final_detections])) if final_detections else 0.0,
            'mean_height': float(np.mean([d.get('height', 2.0) for d in final_detections])) if final_detections else 0.0,
            'mean_width': float(np.mean([d.get('width', 0.15) for d in final_detections])) if final_detections else 0.0,
            'mean_distribution_score': float(np.mean([d.get('distribution_score', 0.0) for d in final_detections])) if final_detections else 0.0
        }
    }
    
    metrics_file = output_path / f'c_section_metrics_{ground_method}.json'
    with open(metrics_file, 'w') as f:
        json.dump(metrics, f, indent=2)
    print(f"Saved metrics: {metrics_file}")
    
    print(f"\nAll results saved to: {output_path}")

# Summary
print("\n" + "="*60)
print("C-SECTION PILE DETECTION SUMMARY")
print("="*60)
print(f"Site: {site_name}")
print(f"Ground method: {ground_method}")
print(f"Detection method: {detection_method_used}")
print(f"PyTorch available: {TORCH_AVAILABLE}")
print(f"Input points: {len(points):,}")
print(f"Geometric detections: {len(geometric_detections)}")
if TORCH_AVAILABLE:
    print(f"PointNet model trained: {'Yes' if pointnet_results.get('model') else 'No'}")
print(f"Final detections: {len(final_detections)}")
print(f"Success rate: {'GOOD' if len(final_detections) > 0 else 'NO DETECTIONS'}")
print("="*60)
print("\nImplementation Summary:")
print("- Clean PointNet implementation for C-section detection")
print("- Synthetic data generation for training")
print("- Complete training pipeline demonstrated")
print("- Geometric baseline for comparison")
print("- Ready for real data training")
print("\nNext steps:")
print("- Replace synthetic data with real labeled data")
print("- Implement inference on point cloud patches")
print("- Add data augmentation")
print("- Optimize hyperparameters")

# C-SECTION PILE DETECTION (WITH POINTNET++)

# SECTION 1: Imports & Parameters
import numpy as np
import pandas as pd
import open3d as o3d
from pathlib import Path
import matplotlib.pyplot as plt
from sklearn.cluster import DBSCAN
from sklearn.neighbors import NearestNeighbors
from sklearn.decomposition import PCA
import json
from datetime import datetime
import tensorflow as tf

# Parameters
site_name = "trino_enel"
ground_method = "csf"
confidence_threshold = 0.5  # LOWERED from 0.6 or 0.7
output_dir = Path("../../../../../data/output_runs/pile_detection") / ground_method
output_dir.mkdir(parents=True, exist_ok=True)
print(f"Saving results to: {output_dir}")

point_cloud_file = Path(f"../../../../../data/output_runs/icp_alignment_corrected/{ground_method}/aligned_ifc_{ground_method}.ply")
detections_file = output_path / f"c_section_detections_{ground_method}.csv"

# === 2. LOAD POINT CLOUD ===
print("Loading point cloud...")
if not point_cloud_file.exists():
    point_cloud_file = Path(f"../../../../../data/ground_segmentation/{ground_method}/ifc_ground.ply")

pcd = o3d.io.read_point_cloud(str(point_cloud_file))
points = np.asarray(pcd.points)

print(f"Loaded {points.shape[0]:,} points from {point_cloud_file}")


# === 3. GEOMETRIC DETECTION ===
class GeometricCSectionDetector:
    def __init__(self):
        self.min_height = 0.8              # lowered from 1.5
        self.width = 0.15
        self.depth = 0.08
        self.width_tol = 0.1               # widened tolerance
        self.min_points = 20               # lowered from 40

    def detect_vertical_structures(self, pts):
        zmin = np.min(pts[:, 2])
        mask = (pts[:, 2] - zmin) >= self.min_height
        elevated = pts[mask]

        print(f"Points above {self.min_height}m: {len(elevated):,}")
        if len(elevated) < self.min_points:
            return []

        labels = DBSCAN(eps=0.4, min_samples=self.min_points).fit_predict(elevated[:, :2])
        structures = [elevated[labels == i] for i in set(labels) if i != -1]

        print(f"Found {len(structures)} vertical structure candidates")
        for i, s in enumerate(structures[:5]):
            print(f"  Cluster {i}: {len(s)} pts, Z-span = {np.ptp(s[:, 2]):.2f} m")

        return structures

    def analyze(self, struct):
        x_span = np.ptp(struct[:, 0])
        y_span = np.ptp(struct[:, 1])
        z_span = np.ptp(struct[:, 2])

        h_score = min(z_span / self.min_height, 1.0)
        max_h = max(x_span, y_span)
        min_h = min(x_span, y_span)

        width_score = 0.7 if (self.width - self.width_tol <= max_h <= self.width + self.width_tol) else 0.0
        aspect = max_h / (min_h + 1e-6)
        aspect_score = 0.8 if 1.5 <= aspect <= 4.0 else 0.0
        dist_score = self.dist_score(struct)

        conf = h_score * 0.3 + width_score * 0.3 + aspect_score * 0.2 + dist_score * 0.2

        return conf, z_span, max_h, min_h, len(struct), dist_score

    def dist_score(self, pts):
        if len(pts) < 20:
            return 0.0
        from sklearn.decomposition import PCA
        centered = pts[:, :2] - np.mean(pts[:, :2], axis=0)
        pca = PCA(n_components=2).fit(centered)
        tr = pca.transform(centered)
        stds = np.std(tr, axis=0)
        ratio = max(stds) / (min(stds) + 1e-6)
        return 0.8 if ratio > 2.0 else 0.6 if ratio > 1.5 else 0.3

    def center(self, pts):
        zmin = pts[:, 2].min()
        base = pts[pts[:, 2] <= zmin + 0.2 * np.ptp(pts[:, 2])]
        return base.mean(axis=0) if len(base) > 0 else pts.mean(axis=0)

    def detect(self, pts, confidence_threshold=0.3):
        print("Running geometric detection...")
        structs = self.detect_vertical_structures(pts)
        results = []
        for i, s in enumerate(structs):
            conf, h, w, d, n, ds = self.analyze(s)
            if conf >= confidence_threshold:
                c = self.center(s)
                results.append({
                    "x": c[0], "y": c[1], "z": c[2], "confidence": conf,
                    "width": w, "height": h, "depth": d,
                    "point_count": n, "distribution_score": ds,
                    "detection_method": "geometric"
                })
            else:
                print(f"Rejected cluster {i}: conf={conf:.2f}, h={h:.2f}, w={w:.2f}, aspect={w/d:.2f}, pts={n}")
        print(f"Detected {len(results)} C-section piles with geometric rules")
        return results


print("Running geometric detection...")
detector = GeometricCSectionDetector()
geo_detections = detector.detect(points)
print(f"Detected {len(geo_detections)} C-section piles with geometric rules")

import matplotlib.pyplot as plt

plt.figure(figsize=(10, 6))
plt.scatter(points[:, 0], points[:, 1], c='gray', s=1, alpha=0.2)
for d in geo_detections:
    plt.scatter(d['x'], d['y'], c='orange', s=50, edgecolor='black')
plt.axis('equal')
plt.title(f"C-section Pile Detections: {len(geo_detections)}")
plt.xlabel("X"), plt.ylabel("Y")
plt.grid(True)
plt.show()


from tabulate import tabulate
print("Geo detections:", tabulate(geo_detections))


# SECTION 4: PointNet++-style model for binary classification

class PointNetPPClassifier(tf.keras.Model):
    def __init__(self, num_classes=2):
        super().__init__()
        self.net = tf.keras.Sequential([
            tf.keras.layers.Conv1D(64, 1, activation='relu'),
            tf.keras.layers.BatchNormalization(),
            tf.keras.layers.Conv1D(128, 1, activation='relu'),
            tf.keras.layers.BatchNormalization(),
            tf.keras.layers.Conv1D(256, 1, activation='relu'),
            tf.keras.layers.BatchNormalization(),
            tf.keras.layers.GlobalMaxPooling1D(),
            tf.keras.layers.Dense(256, activation='relu'),
            tf.keras.layers.Dropout(0.3),
            tf.keras.layers.Dense(128, activation='relu'),
            tf.keras.layers.Dropout(0.3),
            tf.keras.layers.Dense(num_classes, activation='softmax')
        ])

    def call(self, x, training=False):
        return self.net(x, training=training)


# SECTION 5: Patch creation from detections (positive + negative)

from sklearn.neighbors import NearestNeighbors

def extract_patch(center, max_n=128):
    try:
        nn = NearestNeighbors(n_neighbors=min(max_n, len(points)))
        nn.fit(points)
        _, idxs = nn.kneighbors([center])
        patch = points[idxs[0]]
    except:
        return None

    patch -= patch.mean(axis=0)
    norm = np.max(np.linalg.norm(patch, axis=1))
    if norm > 0:
        patch /= norm

    # Pad if too small
    if patch.shape[0] < max_n:
        padding = np.zeros((max_n - patch.shape[0], 3))
        patch = np.vstack([patch, padding])

    return patch


# Extract positive patches
positive_patches = []
failed_positive = 0

for d in geo_detections:
    patch = extract_patch([d['x'], d['y'], d['z']])
    if patch is not None:
        positive_patches.append(patch)
    else:
        failed_positive += 1

# Generate random negative patches
negative_patches = []
neg_centers = points[np.random.choice(len(points), size=1000, replace=False)]
for c in neg_centers:
    patch = extract_patch(c)
    if patch is not None:
        negative_patches.append(patch)

# Prepare dataset
patches = np.array(positive_patches + negative_patches)
labels = np.array([1] * len(positive_patches) + [0] * len(negative_patches))

print(f"\nTraining patches: {patches.shape}")
print(f"Positive: {len(positive_patches)}, ❌ Failed: {failed_positive}, ❌ Negatives: {len(negative_patches)}")


# SECTION 6: Train PointNet++ model + Save History

import matplotlib.pyplot as plt
from datetime import datetime

if len(positive_patches) == 0:
    print("No positive patches found. Skipping training.")
    final_detections = []
else:
    model = PointNetPPClassifier(num_classes=2)
    model.compile(optimizer='adam', loss='sparse_categorical_crossentropy', metrics=['accuracy'])

    history = model.fit(
        patches,
        labels,
        epochs=30,
        batch_size=8,
        validation_split=0.2,
        verbose=1
    )

    # Plot training history
    plt.figure(figsize=(10, 4))
    plt.plot(history.history['accuracy'], label='Train Acc')
    plt.plot(history.history['val_accuracy'], label='Val Acc')
    plt.plot(history.history['loss'], label='Train Loss')
    plt.plot(history.history['val_loss'], label='Val Loss')
    plt.title('PointNet++ Training History')
    plt.xlabel('Epoch')
    plt.ylabel('Accuracy / Loss')
    plt.grid(True)
    plt.legend()
    plt.tight_layout()
    plt.savefig(output_dir / f'pointnetpp_training_history_{ground_method}.png', dpi=300)
    plt.show()

    # Optional: Save model
    model.save(output_dir / f'pointnetpp_model_{ground_method}.h5')
    print("Model and training plot saved.")


    # SECTION 7: Inference on patches
    probs = model.predict(patches)
    confidences = probs[:, 1]  # class 1 = C-section
    preds = confidences >= confidence_threshold

    final_detections = []
    for i, pred in enumerate(preds[:len(positive_patches)]):  # only filter geometric detections
        if pred:
            det = geo_detections[i].copy()
            det['model_confidence'] = float(confidences[i])
            final_detections.append(det)

    print(f"\nFinal detections after PointNet++: {len(final_detections)} / {len(positive_patches)}")


import matplotlib.pyplot as plt

plt.figure(figsize=(10, 6))

# Background point cloud
plt.scatter(points[:, 0], points[:, 1], c='gray', s=1, alpha=0.2, label='All Points')

# Final detections after PointNet++
for d in final_detections:
    plt.scatter(d['x'], d['y'], c='limegreen', s=50, edgecolor='black', label='Final Detection')

plt.axis('equal')
plt.title(f"Final C-section Detections After PointNet++: {len(final_detections)}")
plt.xlabel("X")
plt.ylabel("Y")
plt.grid(True)

# Prevent duplicate legend entries
handles, labels = plt.gca().get_legend_handles_labels()
by_label = dict(zip(labels, handles))
plt.legend(by_label.values(), by_label.keys(), loc='upper right')

plt.show()


import seaborn as sns
plt.figure(figsize=(8, 4))
sns.histplot(confidences, bins=50, kde=True)
plt.axvline(confidence_threshold, color='red', linestyle='--', label=f"Threshold = {confidence_threshold}")
plt.title("Model Predicted Confidence Scores (Class = 1)")
plt.xlabel("Confidence")
plt.ylabel("Count")
plt.legend()
plt.grid(True)
plt.show()

# SECTION 8: Save final detections
df = pd.DataFrame(final_detections)
df.to_csv(output_dir / f"c_section_detections_final.csv", index=False)

metrics = {
    'timestamp': datetime.now().isoformat(),
    'site_name': site_name,
    'ground_method': ground_method,
    'confidence_threshold': confidence_threshold,
    'input_points': len(points),
    'geometric_detections': len(geometric_detections),
    'final_detections': len(final_detections)
}

with open(output_dir / f"metrics.json", 'w') as f:
    json.dump(metrics, f, indent=2)

print("Results saved.")


# SECTION 9: Visualization (Top view)

plt.figure(figsize=(10, 6))
plt.scatter(points[:, 0], points[:, 1], c='gray', s=1, alpha=0.3)
for d in final_detections:
    plt.scatter(d['x'], d['y'], c='orange', s=100, edgecolor='black')
plt.title(f"C-section Pile Detections: {len(final_detections)}")
plt.xlabel("X")
plt.ylabel("Y")
plt.axis("equal")
plt.grid(True)
plt.show()


probs = model.predict(patches, batch_size=64)
print("Min:", np.min(probs[:, 1]), "Max:", np.max(probs[:, 1]), "Mean:", np.mean(probs[:, 1]))
