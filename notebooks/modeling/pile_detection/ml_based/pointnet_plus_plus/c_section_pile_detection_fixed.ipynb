{
 "cells": [
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "# C-Section Pile Detection (PointNet++) \n",
    "\n",
    "This notebook implements working C-section pile detection using geometric rules, with PointNet++ architecture ready for future training.\n",
    "\n",
    "\n",
    "**Stage**: Pile Detection  \n",
    "**Input Data**: Ground-filtered or aligned point cloud  \n",
    "**Output**: Pile center coordinates + types (C-section, cylindrical, etc.)  \n",
    "**Format**: .csv (columns: x, y, z, pile_type, confidence, etc.)  \n",
    "**Method**: Geometric-based detection with PointNet++ for future training\n",
    "\n",
    "**Author**: Preetam <PERSON>li  \n",
    "**Date**: July 2025 \n",
    "**Project**: As-Built Foundation Analysis"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 1,
   "metadata": {
    "tags": [
     "parameters"
    ]
   },
   "outputs": [],
   "source": [
    "# Parameters (Papermill)\n",
    "site_name = \"trino_enel\"\n",
    "ground_method = \"csf\"  # Options: csf, pmf, ransac\n",
    "confidence_threshold = 0.6  # Lower threshold for C-sections (harder to detect)\n",
    "output_dir = \"../../../../../data/output_runs/pile_detection\"\n",
    "use_aligned_data = True  # Use ICP aligned point clouds if available\n",
    "enable_visualization = True\n",
    "save_results = True"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 2,
   "metadata": {},
   "outputs": [
    {
     "name": "stdout",
     "output_type": "stream",
     "text": [
      "C-SECTION PILE DETECTION - CSF\n",
      "Site: trino_enel\n",
      "Output: ../../../../../data/output_runs/pile_detection/csf\n",
      "Use aligned data: Enabled\n",
      "Confidence threshold: 0.6\n"
     ]
    }
   ],
   "source": [
    "# Imports\n",
    "import numpy as np\n",
    "import open3d as o3d\n",
    "import matplotlib.pyplot as plt\n",
    "from pathlib import Path\n",
    "import pandas as pd\n",
    "from sklearn.cluster import DBSCAN\n",
    "from sklearn.neighbors import NearestNeighbors\n",
    "from sklearn.decomposition import PCA\n",
    "import json\n",
    "from datetime import datetime\n",
    "import warnings\n",
    "warnings.filterwarnings('ignore')\n",
    "\n",
    "# Deep learning imports\n",
    "try:\n",
    "    import torch\n",
    "    import torch.nn as nn\n",
    "    import torch.nn.functional as F\n",
    "    from torch.utils.data import Dataset, DataLoader\n",
    "    TORCH_AVAILABLE = True\n",
    "    print(f\"PyTorch version: {torch.__version__}\")\n",
    "    print(f\"CUDA available: {torch.cuda.is_available()}\")\n",
    "    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n",
    "    print(f\"Using device: {device}\")\n",
    "except ImportError:\n",
    "    TORCH_AVAILABLE = False\n",
    "    print(\"PyTorch not available - using geometric detection only\")\n",
    "    device = None\n",
    "\n",
    "# Setup\n",
    "output_path = Path(output_dir) / ground_method\n",
    "output_path.mkdir(parents=True, exist_ok=True)\n",
    "\n",
    "print(f\"C-SECTION PILE DETECTION - {ground_method.upper()}\")\n",
    "print(f\"Site: {site_name}\")\n",
    "print(f\"Output: {output_path}\")\n",
    "print(f\"Use aligned data: {'Enabled' if use_aligned_data else '❌ Disabled'}\")\n",
    "print(f\"Confidence threshold: {confidence_threshold}\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## Load Point Cloud Data"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 3,
   "metadata": {},
   "outputs": [
    {
     "name": "stdout",
     "output_type": "stream",
     "text": [
      "ls: -: No such file or directory\n",
      "ls: lh: No such file or directory\n",
      "../../../../../data:\n",
      "\u001b[34mENEL\u001b[m\u001b[m                         \u001b[34moutput_runs\u001b[m\u001b[m\n",
      "\u001b[34manalysis_output\u001b[m\u001b[m              \u001b[34mprocessed\u001b[m\u001b[m\n",
      "convert_ifc_to_pointcloud.py \u001b[34mraw\u001b[m\u001b[m\n",
      "ifc_metadata.csv             \u001b[34mreference\u001b[m\u001b[m\n",
      "\u001b[34mmlruns\u001b[m\u001b[m\n"
     ]
    }
   ],
   "source": [
    "\n",
    "!ls - lh ../../../../../data"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 4,
   "metadata": {},
   "outputs": [
    {
     "name": "stdout",
     "output_type": "stream",
     "text": [
      "==> LOADING POINT CLOUD DATA\n",
      "Using aligned point cloud: ../../../../../data/output_runs/icp_alignment_corrected/csf/aligned_ifc_csf.ply\n",
      "\n",
      "DATA SOURCE: ICP aligned\n",
      "Points loaded: 1,359,240\n",
      "Bounds: X[435307.2, 436760.1] Y[5010836.8, 5012398.5] Z[-0.7, 6.1]\n"
     ]
    }
   ],
   "source": [
    "from pathlib import Path\n",
    "import numpy as np\n",
    "import open3d as o3d\n",
    "\n",
    "# Load point cloud data, preferring aligned ICP output if available\n",
    "print(\"==> LOADING POINT CLOUD DATA\")\n",
    "\n",
    "aligned_file = Path(f\"../../../../../data/output_runs/icp_alignment_corrected/{ground_method}/aligned_ifc_{ground_method}.ply\")\n",
    "if aligned_file.exists():\n",
    "    input_file = aligned_file\n",
    "    data_source = \"ICP aligned\"\n",
    "    print(f\"Using aligned point cloud: {input_file}\")\n",
    "else:\n",
    "    print(f\"Aligned file not found: {aligned_file}\")\n",
    "    print(\"Falling back to ground segmentation data\")\n",
    "    input_file = Path(f\"../../../../../data/ground_segmentation/{ground_method}/ifc_ground.ply\")\n",
    "    data_source = \"Ground segmentation\"\n",
    "\n",
    "# Load point cloud\n",
    "pcd = o3d.io.read_point_cloud(str(input_file))\n",
    "points = np.asarray(pcd.points)\n",
    "\n",
    "# Display summary\n",
    "print(f\"\\nDATA SOURCE: {data_source}\")\n",
    "print(f\"Points loaded: {points.shape[0]:,}\")\n",
    "print(f\"Bounds: X[{points[:, 0].min():.1f}, {points[:, 0].max():.1f}] \"\n",
    "      f\"Y[{points[:, 1].min():.1f}, {points[:, 1].max():.1f}] \"\n",
    "      f\"Z[{points[:, 2].min():.1f}, {points[:, 2].max():.1f}]\")\n"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## PointNet++ Architecture for C-Section Detection\n",
    "\n",
    "Let's start by implementing a PointNet++ model for C-section pile detection. This is an exploratory implementation to understand how deep learning can be applied to this problem."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "if TORCH_AVAILABLE:\n",
    "    class PointNetSetAbstraction(nn.Module):\n",
    "        \"\"\"PointNet++ Set Abstraction Layer\"\"\"\n",
    "        def __init__(self, npoint, radius, nsample, in_channel, mlp, group_all):\n",
    "            super(PointNetSetAbstraction, self).__init__()\n",
    "            self.npoint = npoint\n",
    "            self.radius = radius\n",
    "            self.nsample = nsample\n",
    "            self.group_all = group_all\n",
    "            \n",
    "            self.mlp_convs = nn.ModuleList()\n",
    "            self.mlp_bns = nn.ModuleList()\n",
    "            last_channel = in_channel\n",
    "            for out_channel in mlp:\n",
    "                self.mlp_convs.append(nn.Conv2d(last_channel, out_channel, 1))\n",
    "                self.mlp_bns.append(nn.BatchNorm2d(out_channel))\n",
    "                last_channel = out_channel\n",
    "        \n",
    "        def forward(self, xyz, points):\n",
    "            \"\"\"\n",
    "            Input:\n",
    "                xyz: input points position data, [B, C, N]\n",
    "                points: input points data, [B, D, N]\n",
    "            Return:\n",
    "                new_xyz: sampled points position data, [B, C, S]\n",
    "                new_points_concat: sample points feature data, [B, D', S]\n",
    "            \"\"\"\n",
    "            xyz = xyz.permute(0, 2, 1)\n",
    "            if points is not None:\n",
    "                points = points.permute(0, 2, 1)\n",
    "            \n",
    "            if self.group_all:\n",
    "                new_xyz, new_points = self.sample_and_group_all(xyz, points)\n",
    "            else:\n",
    "                new_xyz, new_points = self.sample_and_group(xyz, points)\n",
    "            \n",
    "            # new_xyz: sampled points position data, [B, npoint, C]\n",
    "            # new_points: sampled points data, [B, npoint, nsample, C+D]\n",
    "            new_points = new_points.permute(0, 3, 2, 1) # [B, C+D, nsample,npoint]\n",
    "            for i, conv in enumerate(self.mlp_convs):\n",
    "                bn = self.mlp_bns[i]\n",
    "                new_points =  F.relu(bn(conv(new_points)))\n",
    "            \n",
    "            new_points = torch.max(new_points, 2)[0]\n",
    "            new_xyz = new_xyz.permute(0, 2, 1)\n",
    "            return new_xyz, new_points\n",
    "        \n",
    "        def sample_and_group(self, xyz, points):\n",
    "            \"\"\"Sample and group points\"\"\"\n",
    "            B, N, C = xyz.shape\n",
    "            S = self.npoint\n",
    "            \n",
    "            # Farthest point sampling\n",
    "            fps_idx = self.farthest_point_sample(xyz, self.npoint) # [B, npoint, C]\n",
    "            new_xyz = self.index_points(xyz, fps_idx)\n",
    "            \n",
    "            # Ball query\n",
    "            idx = self.query_ball_point(self.radius, self.nsample, xyz, new_xyz)\n",
    "            grouped_xyz = self.index_points(xyz, idx) # [B, npoint, nsample, C]\n",
    "            grouped_xyz_norm = grouped_xyz - new_xyz.view(B, S, 1, C)\n",
    "            \n",
    "            if points is not None:\n",
    "                grouped_points = self.index_points(points, idx)\n",
    "                new_points = torch.cat([grouped_xyz_norm, grouped_points], dim=-1) # [B, npoint, nsample, C+D]\n",
    "            else:\n",
    "                new_points = grouped_xyz_norm\n",
    "            \n",
    "            return new_xyz, new_points\n",
    "        \n",
    "        def sample_and_group_all(self, xyz, points):\n",
    "            \"\"\"Sample and group all points\"\"\"\n",
    "            device = xyz.device\n",
    "            B, N, C = xyz.shape\n",
    "            new_xyz = torch.zeros(B, 1, C).to(device)\n",
    "            grouped_xyz = xyz.view(B, 1, N, C)\n",
    "            if points is not None:\n",
    "                new_points = torch.cat([grouped_xyz, points.view(B, 1, N, -1)], dim=-1)\n",
    "            else:\n",
    "                new_points = grouped_xyz\n",
    "            return new_xyz, new_points\n",
    "        \n",
    "        def farthest_point_sample(self, xyz, npoint):\n",
    "            \"\"\"Farthest point sampling\"\"\"\n",
    "            device = xyz.device\n",
    "            B, N, C = xyz.shape\n",
    "            centroids = torch.zeros(B, npoint, dtype=torch.long).to(device)\n",
    "            distance = torch.ones(B, N).to(device) * 1e10\n",
    "            farthest = torch.randint(0, N, (B,), dtype=torch.long).to(device)\n",
    "            batch_indices = torch.arange(B, dtype=torch.long).to(device)\n",
    "            \n",
    "            for i in range(npoint):\n",
    "                centroids[:, i] = farthest\n",
    "                centroid = xyz[batch_indices, farthest, :].view(B, 1, 3)\n",
    "                dist = torch.sum((xyz - centroid) ** 2, -1)\n",
    "                mask = dist < distance\n",
    "                distance[mask] = dist[mask]\n",
    "                farthest = torch.max(distance, -1)[1]\n",
    "            \n",
    "            return centroids\n",
    "        \n",
    "        def query_ball_point(self, radius, nsample, xyz, new_xyz):\n",
    "            \"\"\"Ball query\"\"\"\n",
    "            device = xyz.device\n",
    "            B, N, C = xyz.shape\n",
    "            _, S, _ = new_xyz.shape\n",
    "            group_idx = torch.arange(N, dtype=torch.long).to(device).view(1, 1, N).repeat([B, S, 1])\n",
    "            sqrdists = self.square_distance(new_xyz, xyz)\n",
    "            group_idx[sqrdists > radius ** 2] = N\n",
    "            group_idx = group_idx.sort(dim=-1)[0][:, :, :nsample]\n",
    "            group_first = group_idx[:, :, 0].view(B, S, 1).repeat([1, 1, nsample])\n",
    "            mask = group_idx == N\n",
    "            group_idx[mask] = group_first[mask]\n",
    "            return group_idx\n",
    "        \n",
    "        def square_distance(self, src, dst):\n",
    "            \"\"\"Calculate squared distance\"\"\"\n",
    "            B, N, _ = src.shape\n",
    "            _, M, _ = dst.shape\n",
    "            dist = -2 * torch.matmul(src, dst.permute(0, 2, 1))\n",
    "            dist += torch.sum(src ** 2, -1).view(B, N, 1)\n",
    "            dist += torch.sum(dst ** 2, -1).view(B, 1, M)\n",
    "            return dist\n",
    "        \n",
    "        def index_points(self, points, idx):\n",
    "            \"\"\"Index points\"\"\"\n",
    "            device = points.device\n",
    "            B = points.shape[0]\n",
    "            view_shape = list(idx.shape)\n",
    "            view_shape[1:] = [1] * (len(view_shape) - 1)\n",
    "            repeat_shape = list(idx.shape)\n",
    "            repeat_shape[0] = 1\n",
    "            batch_indices = torch.arange(B, dtype=torch.long).to(device).view(view_shape).repeat(repeat_shape)\n",
    "            new_points = points[batch_indices, idx, :]\n",
    "            return new_points\n",
    "    \n",
    "    print(\"PointNet++ Set Abstraction layer implemented\")\n",
    "else:\n",
    "    print(\"Skipping PointNet++ implementation - PyTorch not available\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "if TORCH_AVAILABLE:\n",
    "    class PointNetPlusPlusCSectionDetector(nn.Module):\n",
    "        \"\"\"PointNet++ model for C-section pile detection\"\"\"\n",
    "        def __init__(self, num_classes=2):\n",
    "            super(PointNetPlusPlusCSectionDetector, self).__init__()\n",
    "            \n",
    "            # Set abstraction layers\n",
    "            self.sa1 = PointNetSetAbstraction(npoint=512, radius=0.2, nsample=32, in_channel=6, mlp=[64, 64, 128], group_all=False)\n",
    "            self.sa2 = PointNetSetAbstraction(npoint=128, radius=0.4, nsample=64, in_channel=128 + 3, mlp=[128, 128, 256], group_all=False)\n",
    "            self.sa3 = PointNetSetAbstraction(npoint=None, radius=None, nsample=None, in_channel=256 + 3, mlp=[256, 512, 1024], group_all=True)\n",
    "            \n",
    "            # Classification head\n",
    "            self.fc1 = nn.Linear(1024, 512)\n",
    "            self.bn1 = nn.BatchNorm1d(512)\n",
    "            self.drop1 = nn.Dropout(0.4)\n",
    "            self.fc2 = nn.Linear(512, 256)\n",
    "            self.bn2 = nn.BatchNorm1d(256)\n",
    "            self.drop2 = nn.Dropout(0.4)\n",
    "            self.fc3 = nn.Linear(256, num_classes)\n",
    "        \n",
    "        def forward(self, xyz):\n",
    "            \"\"\"\n",
    "            Forward pass\n",
    "            Input: xyz [B, 3, N] - point coordinates\n",
    "            Output: classification scores [B, num_classes]\n",
    "            \"\"\"\n",
    "            B, _, _ = xyz.shape\n",
    "            \n",
    "            # Add normal vectors as features (simplified - using coordinate differences)\n",
    "            if xyz.shape[1] == 3:\n",
    "                # Create simple features from coordinates\n",
    "                features = torch.cat([\n",
    "                    xyz,\n",
    "                    xyz - xyz.mean(dim=2, keepdim=True),  # Centered coordinates\n",
    "                    torch.norm(xyz, dim=1, keepdim=True).repeat(1, 3, 1)  # Distance from origin\n",
    "                ], dim=1)\n",
    "            else:\n",
    "                features = xyz\n",
    "            \n",
    "            # Set abstraction layers\n",
    "            l1_xyz, l1_points = self.sa1(xyz, features)\n",
    "            l2_xyz, l2_points = self.sa2(l1_xyz, l1_points)\n",
    "            l3_xyz, l3_points = self.sa3(l2_xyz, l2_points)\n",
    "            \n",
    "            # Global feature\n",
    "            x = l3_points.view(B, 1024)\n",
    "            \n",
    "            # Classification\n",
    "            x = self.drop1(F.relu(self.bn1(self.fc1(x))))\n",
    "            x = self.drop2(F.relu(self.bn2(self.fc2(x))))\n",
    "            x = self.fc3(x)\n",
    "            \n",
    "            return F.log_softmax(x, dim=1)\n",
    "    \n",
    "    print(\"PointNet++ C-section detector model implemented\")\n",
    "else:\n",
    "    print(\"Skipping PointNet++ model - PyTorch not available\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## Point Cloud Patch Generation\n",
    "\n",
    "For PointNet++, we need to create patches from the point cloud. Each patch will be classified as containing a C-section pile or not."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "class PointCloudPatchDataset(Dataset):\n",
    "    \"\"\"Dataset for point cloud patches\"\"\"\n",
    "    def __init__(self, patches, labels=None, num_points=1024):\n",
    "        self.patches = patches\n",
    "        self.labels = labels\n",
    "        self.num_points = num_points\n",
    "    \n",
    "    def __len__(self):\n",
    "        return len(self.patches)\n",
    "    \n",
    "    def __getitem__(self, idx):\n",
    "        patch = self.patches[idx]\n",
    "        \n",
    "        # Normalize patch to unit sphere\n",
    "        centroid = patch.mean(axis=0)\n",
    "        patch = patch - centroid\n",
    "        max_dist = np.max(np.linalg.norm(patch, axis=1))\n",
    "        if max_dist > 0:\n",
    "            patch = patch / max_dist\n",
    "        \n",
    "        # Resample to fixed number of points\n",
    "        if len(patch) >= self.num_points:\n",
    "            # Random sampling\n",
    "            indices = np.random.choice(len(patch), self.num_points, replace=False)\n",
    "            patch = patch[indices]\n",
    "        else:\n",
    "            # Upsample with repetition\n",
    "            indices = np.random.choice(len(patch), self.num_points, replace=True)\n",
    "            patch = patch[indices]\n",
    "        \n",
    "        patch = torch.FloatTensor(patch).transpose(0, 1)  # [3, N]\n",
    "        \n",
    "        if self.labels is not None:\n",
    "            label = torch.LongTensor([self.labels[idx]])\n",
    "            return patch, label\n",
    "        else:\n",
    "            return patch\n",
    "\n",
    "def generate_point_cloud_patches(points, patch_size=2.0, overlap=0.5, min_points=50):\n",
    "    \"\"\"Generate overlapping patches from point cloud\"\"\"\n",
    "    print(f\"Generating patches with size {patch_size}m, overlap {overlap}\")\n",
    "    \n",
    "    # Calculate grid bounds\n",
    "    x_min, y_min = points[:, 0].min(), points[:, 1].min()\n",
    "    x_max, y_max = points[:, 0].max(), points[:, 1].max()\n",
    "    \n",
    "    step_size = patch_size * (1 - overlap)\n",
    "    \n",
    "    patches = []\n",
    "    patch_centers = []\n",
    "    \n",
    "    x = x_min\n",
    "    while x < x_max:\n",
    "        y = y_min\n",
    "        while y < y_max:\n",
    "            # Define patch bounds\n",
    "            x_patch_min, x_patch_max = x, x + patch_size\n",
    "            y_patch_min, y_patch_max = y, y + patch_size\n",
    "            \n",
    "            # Extract points in patch\n",
    "            mask = ((points[:, 0] >= x_patch_min) & (points[:, 0] <= x_patch_max) &\n",
    "                   (points[:, 1] >= y_patch_min) & (points[:, 1] <= y_patch_max))\n",
    "            \n",
    "            patch_points = points[mask]\n",
    "            \n",
    "            if len(patch_points) >= min_points:\n",
    "                patches.append(patch_points)\n",
    "                patch_centers.append([x + patch_size/2, y + patch_size/2])\n",
    "            \n",
    "            y += step_size\n",
    "        x += step_size\n",
    "    \n",
    "    print(f\"Generated {len(patches)} patches\")\n",
    "    return patches, np.array(patch_centers)\n",
    "\n",
    "print(\"Patch generation utilities implemented\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## Geometric-Based C-Section Pile Detection (Baseline)\n",
    "\n",
    "Let's keep the geometric approach as our baseline and comparison method."
   ]
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 5,
   "metadata": {},
   "outputs": [
    {
     "name": "stdout",
     "output_type": "stream",
     "text": [
      "Geometric C-section detector initialized\n"
     ]
    }
   ],
   "source": [
    "class GeometricCSectionDetector:\n",
    "    \"\"\"Geometric-based C-section pile detection using point cloud analysis.\"\"\"\n",
    "    \n",
    "    def __init__(self):\n",
    "        # C-section pile characteristics\n",
    "        self.min_height = 1.5  # Minimum pile height above ground\n",
    "        self.max_height = 10.0  # Maximum reasonable pile height\n",
    "        self.typical_width = 0.15  # Typical C-section width (~15cm)\n",
    "        self.typical_depth = 0.08  # Typical C-section depth (~8cm)\n",
    "        self.width_tolerance = 0.08  # ±8cm tolerance\n",
    "        self.min_points_per_pile = 40  # Minimum points (C-sections have fewer points than I-sections)\n",
    "        \n",
    "    def detect_vertical_structures(self, points):\n",
    "        \"\"\"Detect vertical structures that could be C-section piles.\"\"\"\n",
    "        print(\"Detecting vertical structures...\")\n",
    "        \n",
    "        # Filter by height\n",
    "        z_min = points[:, 2].min()\n",
    "        height_mask = (points[:, 2] - z_min) >= self.min_height\n",
    "        elevated_points = points[height_mask]\n",
    "        \n",
    "        print(f\"Points above {self.min_height}m: {len(elevated_points):,}\")\n",
    "        \n",
    "        if len(elevated_points) < self.min_points_per_pile:\n",
    "            return []\n",
    "        \n",
    "        # Use smaller clustering radius for C-sections (they're thinner)\n",
    "        clustering = DBSCAN(eps=0.3, min_samples=self.min_points_per_pile)\n",
    "        cluster_labels = clustering.fit_predict(elevated_points[:, :2])\n",
    "        \n",
    "        # Extract clusters\n",
    "        unique_labels = set(cluster_labels)\n",
    "        unique_labels.discard(-1)  # Remove noise\n",
    "        \n",
    "        vertical_structures = []\n",
    "        for label in unique_labels:\n",
    "            cluster_mask = cluster_labels == label\n",
    "            cluster_points = elevated_points[cluster_mask]\n",
    "            \n",
    "            if len(cluster_points) >= self.min_points_per_pile:\n",
    "                vertical_structures.append(cluster_points)\n",
    "        \n",
    "        print(f\"Found {len(vertical_structures)} vertical structure candidates\")\n",
    "        return vertical_structures\n",
    "    \n",
    "    def analyze_c_section_characteristics(self, structure_points):\n",
    "        \"\"\"Analyze if structure has C-section characteristics.\"\"\"\n",
    "        # Calculate structure dimensions\n",
    "        x_span = structure_points[:, 0].max() - structure_points[:, 0].min()\n",
    "        y_span = structure_points[:, 1].max() - structure_points[:, 1].min()\n",
    "        z_span = structure_points[:, 2].max() - structure_points[:, 2].min()\n",
    "        \n",
    "        # C-section characteristics:\n",
    "        # 1. Significant height (vertical structure)\n",
    "        # 2. Moderate width (C-section opening)\n",
    "        # 3. Smaller depth (C-section profile)\n",
    "        # 4. Asymmetric cross-section (C-shape)\n",
    "        \n",
    "        height_score = min(z_span / self.min_height, 1.0)\n",
    "        \n",
    "        # Check dimensions for C-section profile\n",
    "        max_horizontal = max(x_span, y_span)\n",
    "        min_horizontal = min(x_span, y_span)\n",
    "        \n",
    "        # C-sections are typically smaller than I-sections\n",
    "        width_score = 0.0\n",
    "        if (self.typical_width - self.width_tolerance) <= max_horizontal <= (self.typical_width + self.width_tolerance):\n",
    "            width_score = 0.7\n",
    "        \n",
    "        # C-sections have moderate aspect ratio (not as elongated as I-sections)\n",
    "        aspect_ratio = max_horizontal / (min_horizontal + 1e-6)\n",
    "        aspect_score = 0.0\n",
    "        if 1.5 <= aspect_ratio <= 4.0:  # C-sections less elongated than I-sections\n",
    "            aspect_score = 0.8\n",
    "        \n",
    "        # Analyze point distribution for C-shape characteristics\n",
    "        distribution_score = self.analyze_point_distribution(structure_points)\n",
    "        \n",
    "        # Combine scores (weight distribution more heavily for C-sections)\n",
    "        confidence = (height_score * 0.3 + width_score * 0.3 + \n",
    "                     aspect_score * 0.2 + distribution_score * 0.2)\n",
    "        \n",
    "        return {\n",
    "            'confidence': confidence,\n",
    "            'height': z_span,\n",
    "            'width': max_horizontal,\n",
    "            'depth': min_horizontal,\n",
    "            'aspect_ratio': aspect_ratio,\n",
    "            'point_count': len(structure_points),\n",
    "            'distribution_score': distribution_score\n",
    "        }\n",
    "    \n",
    "    def analyze_point_distribution(self, structure_points):\n",
    "        \"\"\"Analyze point distribution to detect C-shape characteristics.\"\"\"\n",
    "        if len(structure_points) < 20:\n",
    "            return 0.0\n",
    "        \n",
    "        # Project points to horizontal plane and analyze distribution\n",
    "        xy_points = structure_points[:, :2]\n",
    "        \n",
    "        # Center the points\n",
    "        centroid = xy_points.mean(axis=0)\n",
    "        centered_points = xy_points - centroid\n",
    "        \n",
    "        # Use PCA to find principal directions\n",
    "        pca = PCA(n_components=2)\n",
    "        pca.fit(centered_points)\n",
    "        \n",
    "        # Transform to principal component space\n",
    "        transformed_points = pca.transform(centered_points)\n",
    "        \n",
    "        # Analyze distribution along principal axes\n",
    "        pc1_std = np.std(transformed_points[:, 0])\n",
    "        pc2_std = np.std(transformed_points[:, 1])\n",
    "        \n",
    "        # C-sections should have asymmetric distribution\n",
    "        asymmetry_ratio = max(pc1_std, pc2_std) / (min(pc1_std, pc2_std) + 1e-6)\n",
    "        \n",
    "        # Score based on asymmetry (C-sections are more asymmetric than cylinders)\n",
    "        if asymmetry_ratio > 2.0:\n",
    "            return 0.8\n",
    "        elif asymmetry_ratio > 1.5:\n",
    "            return 0.6\n",
    "        else:\n",
    "            return 0.3\n",
    "    \n",
    "    def extract_pile_center(self, structure_points):\n",
    "        \"\"\"Extract pile center coordinates.\"\"\"\n",
    "        # Use centroid of bottom 20% of points\n",
    "        z_min = structure_points[:, 2].min()\n",
    "        z_threshold = z_min + 0.2 * (structure_points[:, 2].max() - z_min)\n",
    "        \n",
    "        base_mask = structure_points[:, 2] <= z_threshold\n",
    "        base_points = structure_points[base_mask]\n",
    "        \n",
    "        if len(base_points) > 0:\n",
    "            center = base_points.mean(axis=0)\n",
    "        else:\n",
    "            center = structure_points.mean(axis=0)\n",
    "        \n",
    "        return center\n",
    "    \n",
    "    def detect_c_section_piles(self, points):\n",
    "        \"\"\"Main detection pipeline.\"\"\"\n",
    "        print(\"Starting C-section pile detection...\")\n",
    "        \n",
    "        # Step 1: Find vertical structures\n",
    "        vertical_structures = self.detect_vertical_structures(points)\n",
    "        \n",
    "        if not vertical_structures:\n",
    "            print(\"No vertical structures found\")\n",
    "            return []\n",
    "        \n",
    "        # Step 2: Analyze each structure for C-section characteristics\n",
    "        detections = []\n",
    "        for i, structure in enumerate(vertical_structures):\n",
    "            analysis = self.analyze_c_section_characteristics(structure)\n",
    "            \n",
    "            if analysis['confidence'] >= confidence_threshold:\n",
    "                center = self.extract_pile_center(structure)\n",
    "                \n",
    "                detection = {\n",
    "                    'x': float(center[0]),\n",
    "                    'y': float(center[1]),\n",
    "                    'z': float(center[2]),\n",
    "                    'confidence': float(analysis['confidence']),\n",
    "                    'width': float(analysis['width']),\n",
    "                    'height': float(analysis['height']),\n",
    "                    'depth': float(analysis['depth']),\n",
    "                    'c_section_score': float(analysis['confidence']),\n",
    "                    'point_count': int(analysis['point_count']),\n",
    "                    'distribution_score': float(analysis['distribution_score']),\n",
    "                    'detection_method': 'geometric'\n",
    "                }\n",
    "                \n",
    "                detections.append(detection)\n",
    "        \n",
    "        print(f\"Detected {len(detections)} C-section pile candidates\")\n",
    "        return detections\n",
    "\n",
    "# Initialize detector\n",
    "detector = GeometricCSectionDetector()\n",
    "print(\"Geometric C-section detector initialized\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## PointNet++ Patch-Based Detection\n",
    "\n",
    "Now let's use PointNet++ to analyze patches for C-section pile detection. Since we don't have a trained model yet, we'll demonstrate the inference pipeline with a randomly initialized model."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "if TORCH_AVAILABLE:\n",
    "    print(\"==> POINTNET++ PATCH-BASED DETECTION\")\n",
    "    \n",
    "    # Generate patches from the point cloud\n",
    "    patches, patch_centers = generate_point_cloud_patches(points, patch_size=3.0, overlap=0.3, min_points=100)\n",
    "    \n",
    "    if len(patches) > 0:\n",
    "        # Initialize model (randomly - in practice you'd load a trained model)\n",
    "        model = PointNetPlusPlusCSectionDetector(num_classes=2)\n",
    "        model.to(device)\n",
    "        model.eval()\n",
    "        \n",
    "        print(f\"Model initialized on {device}\")\n",
    "        print(f\"Processing {len(patches)} patches...\")\n",
    "        \n",
    "        # Create dataset and dataloader\n",
    "        dataset = PointCloudPatchDataset(patches, num_points=1024)\n",
    "        dataloader = DataLoader(dataset, batch_size=8, shuffle=False)\n",
    "        \n",
    "        # Run inference\n",
    "        pointnet_detections = []\n",
    "        patch_scores = []\n",
    "        \n",
    "        with torch.no_grad():\n",
    "            for i, batch_patches in enumerate(dataloader):\n",
    "                batch_patches = batch_patches.to(device)\n",
    "                \n",
    "                # Forward pass\n",
    "                outputs = model(batch_patches)\n",
    "                probabilities = torch.exp(outputs)  # Convert log probabilities to probabilities\n",
    "                \n",
    "                # Get C-section scores (class 1)\n",
    "                c_section_scores = probabilities[:, 1].cpu().numpy()\n",
    "                patch_scores.extend(c_section_scores)\n",
    "                \n",
    "                if i % 50 == 0:\n",
    "                    print(f\"  Processed {i * 8}/{len(patches)} patches\")\n",
    "        \n",
    "        patch_scores = np.array(patch_scores)\n",
    "        \n",
    "        # Filter patches with high C-section probability\n",
    "        high_confidence_mask = patch_scores > 0.7  # Threshold for C-section detection\n",
    "        \n",
    "        print(f\"\\nPointNet++ Results:\")\n",
    "        print(f\"  Total patches: {len(patches)}\")\n",
    "        print(f\"  High confidence patches: {np.sum(high_confidence_mask)}\")\n",
    "        print(f\"  Mean score: {np.mean(patch_scores):.3f}\")\n",
    "        print(f\"  Max score: {np.max(patch_scores):.3f}\")\n",
    "        \n",
    "        # Convert high-confidence patches to pile detections\n",
    "        if np.sum(high_confidence_mask) > 0:\n",
    "            high_conf_centers = patch_centers[high_confidence_mask]\n",
    "            high_conf_scores = patch_scores[high_confidence_mask]\n",
    "            \n",
    "            for center, score in zip(high_conf_centers, high_conf_scores):\n",
    "                # Estimate Z coordinate from nearby points\n",
    "                nearby_mask = ((points[:, 0] - center[0])**2 + (points[:, 1] - center[1])**2) < 1.0\n",
    "                if np.sum(nearby_mask) > 0:\n",
    "                    z_coord = points[nearby_mask, 2].mean()\n",
    "                else:\n",
    "                    z_coord = points[:, 2].mean()\n",
    "                \n",
    "                detection = {\n",
    "                    'x': float(center[0]),\n",
    "                    'y': float(center[1]),\n",
    "                    'z': float(z_coord),\n",
    "                    'confidence': float(score),\n",
    "                    'width': 0.15,  # Typical C-section width\n",
    "                    'height': 2.0,  # Estimated height\n",
    "                    'depth': 0.08,  # Typical C-section depth\n",
    "                    'detection_method': 'pointnet++'\n",
    "                }\n",
    "                pointnet_detections.append(detection)\n",
    "        \n",
    "        print(f\"  Final detections: {len(pointnet_detections)}\")\n",
    "        \n",
    "        # Store for comparison\n",
    "        pointnet_results = {\n",
    "            'detections': pointnet_detections,\n",
    "            'patch_scores': patch_scores,\n",
    "            'patch_centers': patch_centers\n",
    "        }\n",
    "    else:\n",
    "        print(\"No patches generated - insufficient point density\")\n",
    "        pointnet_results = {'detections': [], 'patch_scores': [], 'patch_centers': []}\n",
    "else:\n",
    "    print(\"Skipping PointNet++ detection - PyTorch not available\")\n",
    "    pointnet_results = {'detections': [], 'patch_scores': [], 'patch_centers': []}"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## Geometric Detection (Baseline Comparison)"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 6,
   "metadata": {},
   "outputs": [
    {
     "name": "stdout",
     "output_type": "stream",
     "text": [
      "Starting C-section pile detection...\n",
      "Detecting vertical structures...\n",
      "Points above 1.5m: 1,200,073\n",
      "Found 14460 vertical structure candidates\n",
      "Detected 0 C-section pile candidates\n",
      "\n",
      "C-SECTION PILE DETECTION RESULTS\n",
      "Total detections: 0\n",
      "Confidence threshold: 0.6\n",
      "No C-section piles detected above confidence threshold\n",
      "C-sections are harder to detect - consider lowering confidence_threshold\n"
     ]
    }
   ],
   "source": [
    "# Run geometric detection\n",
    "geometric_detections = detector.detect_c_section_piles(points)\n",
    "\n",
    "print(f\"\\n==> DETECTION RESULTS COMPARISON\")\n",
    "print(f\"\\nGeometric Method:\")\n",
    "print(f\"  Total detections: {len(geometric_detections)}\")\n",
    "print(f\"  Confidence threshold: {confidence_threshold}\")\n",
    "\n",
    "if TORCH_AVAILABLE:\n",
    "    print(f\"\\nPointNet++ Method:\")\n",
    "    print(f\"  Total detections: {len(pointnet_results['detections'])}\")\n",
    "    print(f\"  Patches analyzed: {len(pointnet_results['patch_scores'])}\")\n",
    "    if len(pointnet_results['patch_scores']) > 0:\n",
    "        print(f\"  Mean patch score: {np.mean(pointnet_results['patch_scores']):.3f}\")\n",
    "\n",
    "# Combine results for final output (prefer PointNet++ if available, otherwise geometric)\n",
    "if TORCH_AVAILABLE and len(pointnet_results['detections']) > 0:\n",
    "    final_detections = pointnet_results['detections']\n",
    "    detection_method_used = 'pointnet++'\n",
    "    print(f\"\\nUsing PointNet++ detections for final results\")\n",
    "elif len(geometric_detections) > 0:\n",
    "    final_detections = geometric_detections\n",
    "    detection_method_used = 'geometric'\n",
    "    print(f\"\\nUsing geometric detections for final results\")\n",
    "else:\n",
    "    final_detections = []\n",
    "    detection_method_used = 'none'\n",
    "    print(f\"\\nNo detections found with either method\")\n",
    "\n",
    "if final_detections:\n",
    "    confidences = [d['confidence'] for d in final_detections]\n",
    "    heights = [d.get('height', 2.0) for d in final_detections]\n",
    "    widths = [d.get('width', 0.15) for d in final_detections]\n",
    "    \n",
    "    print(f\"\\nFinal Detection Statistics ({detection_method_used}):\")\n",
    "    print(f\"  Count: {len(final_detections)}\")\n",
    "    print(f\"  Mean confidence: {np.mean(confidences):.3f}\")\n",
    "    print(f\"  Confidence range: [{np.min(confidences):.3f}, {np.max(confidences):.3f}]\")\n",
    "    print(f\"  Height range: [{np.min(heights):.2f}, {np.max(heights):.2f}] m\")\n",
    "    print(f\"  Width range: [{np.min(widths):.2f}, {np.max(widths):.2f}] m\")\n",
    "    \n",
    "    # Display individual detections\n",
    "    print(f\"\\nDetailed Results:\")\n",
    "    for i, det in enumerate(final_detections[:10]):  # Show first 10\n",
    "        print(f\"  Pile {i+1}: ({det['x']:.1f}, {det['y']:.1f}, {det['z']:.1f}) \"\n",
    "              f\"conf={det['confidence']:.3f} method={det.get('detection_method', 'unknown')}\")\n",
    "    if len(final_detections) > 10:\n",
    "        print(f\"  ... and {len(final_detections) - 10} more\")\n",
    "else:\n",
    "    print(\"\\nNo C-section piles detected with either method\")\n",
    "    print(\"Consider:\")\n",
    "    print(\"  - Lowering confidence threshold\")\n",
    "    print(\"  - Adjusting patch size for PointNet++\")\n",
    "    print(\"  - Training PointNet++ model on labeled data\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## Visualization"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 7,
   "metadata": {},
   "outputs": [
    {
     "name": "stdout",
     "output_type": "stream",
     "text": [
      "No detections to visualize\n"
     ]
    }
   ],
   "source": [
    "if enable_visualization and final_detections:\n",
    "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n",
    "    \n",
    "    # Plot 1: Top view with detections\n",
    "    ax1.scatter(points[:, 0], points[:, 1], c=points[:, 2], s=1, alpha=0.6, cmap='viridis')\n",
    "    \n",
    "    # Overlay detections (use squares for C-sections to distinguish from I-sections)\n",
    "    for det in final_detections:\n",
    "        square = plt.Rectangle((det['x'] - det['width']/2, det['y'] - det['width']/2), \n",
    "                             det['width'], det['width'], \n",
    "                             fill=False, color='orange', linewidth=2)\n",
    "        ax1.add_patch(square)\n",
    "        ax1.text(det['x'], det['y'], f\"{det['confidence']:.2f}\", \n",
    "                ha='center', va='center', color='orange', fontweight='bold')\n",
    "    \n",
    "    ax1.set_xlabel('X (m)')\n",
    "    ax1.set_ylabel('Y (m)')\n",
    "    ax1.set_title(f'C-Section Pile Detection - Top View\\n{len(final_detections)} detections')\n",
    "    ax1.axis('equal')\n",
    "    ax1.grid(True, alpha=0.3)\n",
    "    \n",
    "    # Plot 2: Side view\n",
    "    ax2.scatter(points[:, 0], points[:, 2], c='blue', s=1, alpha=0.6)\n",
    "    \n",
    "    # Overlay detections\n",
    "    for det in final_detections:\n",
    "        ax2.scatter(det['x'], det['z'], c='orange', s=100, marker='s', linewidth=3)\n",
    "        ax2.text(det['x'], det['z'] + 0.5, f\"{det['confidence']:.2f}\", \n",
    "                ha='center', va='bottom', color='orange', fontweight='bold')\n",
    "    \n",
    "    ax2.set_xlabel('X (m)')\n",
    "    ax2.set_ylabel('Z (m)')\n",
    "    ax2.set_title('C-Section Pile Detection - Side View')\n",
    "    ax2.grid(True, alpha=0.3)\n",
    "    \n",
    "    plt.tight_layout()\n",
    "    \n",
    "    if save_results:\n",
    "        plt.savefig(output_path / f'c_section_detection_{ground_method}.png', dpi=300, bbox_inches='tight')\n",
    "        print(f\"Saved visualization: {output_path / f'c_section_detection_{ground_method}.png'}\")\n",
    "    \n",
    "    plt.show()\n",
    "else:\n",
    "    print(\"No detections to visualize\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## Save Results"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 8,
   "metadata": {},
   "outputs": [
    {
     "name": "stdout",
     "output_type": "stream",
     "text": [
      "\n",
      "SAVING RESULTS\n",
      "Saved metrics: ../../../../../data/output_runs/pile_detection/csf/c_section_metrics_csf.json\n",
      "\n",
      "All results saved to: ../../../../../data/output_runs/pile_detection/csf\n"
     ]
    }
   ],
   "source": [
    "if save_results:\n",
    "    print(\"\\nSAVING RESULTS\")\n",
    "    \n",
    "    # Save detections CSV\n",
    "    if final_detections:\n",
    "        df = pd.DataFrame(final_detections)\n",
    "        csv_file = output_path / f'c_section_detections_{ground_method}.csv'\n",
    "        df.to_csv(csv_file, index=False)\n",
    "        print(f\"Saved detections: {csv_file}\")\n",
    "    \n",
    "    # Save metrics\n",
    "    metrics = {\n",
    "        'timestamp': datetime.now().isoformat(),\n",
    "        'site_name': site_name,\n",
    "        'ground_method': ground_method,\n",
    "        'detection_method_used': detection_method_used,\n",
    "        'pytorch_available': TORCH_AVAILABLE,"\n",
    "        'pile_type': 'c_section',\n",
    "        'confidence_threshold': confidence_threshold,\n",
    "        'total_detections': len(final_detections),\n",
    "        'input_points': int(len(points)),\n",
    "        'data_source': data_source,\n",
    "        'use_aligned_data': use_aligned_data,\n",
    "        'input_file': str(input_file),\n",
    "        'detection_statistics': {\n",
    "            'mean_confidence': float(np.mean([d['confidence'] for d in final_detections])) if final_detections else 0.0,\n",
    "            'mean_height': float(np.mean([d.get('height', 2.0) for d in final_detections])) if final_detections else 0.0,\n",
    "            'mean_width': float(np.mean([d.get('width', 0.15) for d in final_detections])) if final_detections else 0.0,\n",
    "            'mean_distribution_score': float(np.mean([d.get('distribution_score', 0.0) for d in final_detections])) if final_detections else 0.0\n",
    "        }\n",
    "    }\n",
    "    \n",
    "    metrics_file = output_path / f'c_section_metrics_{ground_method}.json'\n",
    "    with open(metrics_file, 'w') as f:\n",
    "        json.dump(metrics, f, indent=2)\n",
    "    print(f\"Saved metrics: {metrics_file}\")\n",
    "    \n",
    "    print(f\"\\nAll results saved to: {output_path}\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 9,
   "metadata": {},
   "outputs": [
    {
     "name": "stdout",
     "output_type": "stream",
     "text": [
      "\n",
      "============================================================\n",
      "C-SECTION PILE DETECTION SUMMARY\n",
      "============================================================\n",
      "Site: trino_enel\n",
      "Ground method: csf\n",
      "Detection method: Geometric-based\n",
      "Input points: 1,359,240\n",
      "Detections found: 0\n",
      "Success rate: ⚠️ NO DETECTIONS\n",
      "============================================================\n",
      "\n",
      "Ready for integration with alignment pipeline!\n"
     ]
    }
   ],
   "source": [
    "# Summary\n",
    "print(\"\\n\" + \"=\"*60)\n",
    "print(\"C-SECTION PILE DETECTION SUMMARY\")\n",
    "print(\"=\"*60)\n",
    "print(f\"Site: {site_name}\")\n",
    "print(f\"Ground method: {ground_method}\")\n",
    "print(f\"Detection method: {detection_method_used}\")\n",
    "print(f\"PyTorch available: {TORCH_AVAILABLE}\")\n",
    "print(f\"Input points: {len(points):,}\")\n",
    "print(f\"Geometric detections: {len(geometric_detections)}\")\n",
    "if TORCH_AVAILABLE:\n",
    "    print(f\"PointNet++ detections: {len(pointnet_results['detections'])}\")\n",
    "    print(f\"Patches analyzed: {len(pointnet_results['patch_scores'])}\")\n",
    "print(f\"Final detections: {len(final_detections)}\")\n",
    "print(f\"Success rate: {'GOOD' if len(final_detections) > 0 else 'NO DETECTIONS'}\")\n",
    "print(\"=\"*60)\n",
    "print(\"\\nExploration Summary:\")\n",
    "print(\"- Implemented PointNet++ architecture for C-section detection\")\n",
    "print(\"- Created patch-based inference pipeline\")\n",
    "print(\"- Compared geometric vs deep learning approaches\")\n",
    "print(\"- Ready for model training with labeled data\")\n",
    "print(\"\\nNext steps:\")\n",
    "print(\"- Collect labeled training data\")\n",
    "print(\"- Train PointNet++ model\")\n",
    "print(\"- Fine-tune hyperparameters\")\n",
    "print(\"- Integrate with alignment pipeline\")"
   ]
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "Python 3",
   "language": "python",
   "name": "python3"
  },
  "language_info": {
   "codemirror_mode": {
    "name": "ipython",
    "version": 3
   },
   "file_extension": ".py",
   "mimetype": "text/x-python",
   "name": "python",
   "nbconvert_exporter": "python",
   "pygments_lexer": "ipython3",
   "version": "3.11.11"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 4
}
