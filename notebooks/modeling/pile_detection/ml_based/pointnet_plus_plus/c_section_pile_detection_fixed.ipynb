# Parameters (Papermill)
site_name = "trino_enel"
ground_method = "csf"  # Options: csf, pmf, ransac
confidence_threshold = 0.6  # Lower threshold for C-sections (harder to detect)
output_dir = "../../../../../data/output_runs/pile_detection"
use_aligned_data = True  # Use ICP aligned point clouds if available
enable_visualization = True
save_results = True

# Imports
import numpy as np
import open3d as o3d
import matplotlib.pyplot as plt
from pathlib import Path
import pandas as pd
from sklearn.cluster import DBSCAN
from sklearn.neighbors import NearestNeighbors
from sklearn.decomposition import PCA
import json
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Deep learning imports
try:
    import torch
    import torch.nn as nn
    import torch.nn.functional as F
    from torch.utils.data import Dataset, DataLoader
    TORCH_AVAILABLE = True
    print(f"PyTorch version: {torch.__version__}")
    print(f"CUDA available: {torch.cuda.is_available()}")
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
except ImportError:
    TORCH_AVAILABLE = False
    print("PyTorch not available - using geometric detection only")
    device = None

# Setup
output_path = Path(output_dir) / ground_method
output_path.mkdir(parents=True, exist_ok=True)

print(f"C-SECTION PILE DETECTION - {ground_method.upper()}")
print(f"Site: {site_name}")
print(f"Output: {output_path}")
print(f"Use aligned data: {'Enabled' if use_aligned_data else '❌ Disabled'}")
print(f"Confidence threshold: {confidence_threshold}")


!ls - lh ../../../../../data

from pathlib import Path
import numpy as np
import open3d as o3d

# Load point cloud data, preferring aligned ICP output if available
print("==> LOADING POINT CLOUD DATA")

aligned_file = Path(f"../../../../../data/output_runs/icp_alignment_corrected/{ground_method}/aligned_ifc_{ground_method}.ply")
if aligned_file.exists():
    input_file = aligned_file
    data_source = "ICP aligned"
    print(f"Using aligned point cloud: {input_file}")
else:
    print(f"Aligned file not found: {aligned_file}")
    print("Falling back to ground segmentation data")
    input_file = Path(f"../../../../../data/ground_segmentation/{ground_method}/ifc_ground.ply")
    data_source = "Ground segmentation"

# Load point cloud
pcd = o3d.io.read_point_cloud(str(input_file))
points = np.asarray(pcd.points)

# Display summary
print(f"\nDATA SOURCE: {data_source}")
print(f"Points loaded: {points.shape[0]:,}")
print(f"Bounds: X[{points[:, 0].min():.1f}, {points[:, 0].max():.1f}] "
      f"Y[{points[:, 1].min():.1f}, {points[:, 1].max():.1f}] "
      f"Z[{points[:, 2].min():.1f}, {points[:, 2].max():.1f}]")


if TORCH_AVAILABLE:
    class PointNetSetAbstraction(nn.Module):
        """PointNet++ Set Abstraction Layer"""
        def __init__(self, npoint, radius, nsample, in_channel, mlp, group_all):
            super(PointNetSetAbstraction, self).__init__()
            self.npoint = npoint
            self.radius = radius
            self.nsample = nsample
            self.group_all = group_all
            
            self.mlp_convs = nn.ModuleList()
            self.mlp_bns = nn.ModuleList()
            last_channel = in_channel
            for out_channel in mlp:
                self.mlp_convs.append(nn.Conv2d(last_channel, out_channel, 1))
                self.mlp_bns.append(nn.BatchNorm2d(out_channel))
                last_channel = out_channel
        
        def forward(self, xyz, points):
            """
            Input:
                xyz: input points position data, [B, C, N]
                points: input points data, [B, D, N]
            Return:
                new_xyz: sampled points position data, [B, C, S]
                new_points_concat: sample points feature data, [B, D', S]
            """
            xyz = xyz.permute(0, 2, 1)
            if points is not None:
                points = points.permute(0, 2, 1)
            
            if self.group_all:
                new_xyz, new_points = self.sample_and_group_all(xyz, points)
            else:
                new_xyz, new_points = self.sample_and_group(xyz, points)
            
            # new_xyz: sampled points position data, [B, npoint, C]
            # new_points: sampled points data, [B, npoint, nsample, C+D]
            new_points = new_points.permute(0, 3, 2, 1) # [B, C+D, nsample,npoint]
            for i, conv in enumerate(self.mlp_convs):
                bn = self.mlp_bns[i]
                new_points =  F.relu(bn(conv(new_points)))
            
            new_points = torch.max(new_points, 2)[0]
            new_xyz = new_xyz.permute(0, 2, 1)
            return new_xyz, new_points
        
        def sample_and_group(self, xyz, points):
            """Sample and group points"""
            B, N, C = xyz.shape
            S = self.npoint
            
            # Farthest point sampling
            fps_idx = self.farthest_point_sample(xyz, self.npoint) # [B, npoint, C]
            new_xyz = self.index_points(xyz, fps_idx)
            
            # Ball query
            idx = self.query_ball_point(self.radius, self.nsample, xyz, new_xyz)
            grouped_xyz = self.index_points(xyz, idx) # [B, npoint, nsample, C]
            grouped_xyz_norm = grouped_xyz - new_xyz.view(B, S, 1, C)
            
            if points is not None:
                grouped_points = self.index_points(points, idx)
                new_points = torch.cat([grouped_xyz_norm, grouped_points], dim=-1) # [B, npoint, nsample, C+D]
            else:
                new_points = grouped_xyz_norm
            
            return new_xyz, new_points
        
        def sample_and_group_all(self, xyz, points):
            """Sample and group all points"""
            device = xyz.device
            B, N, C = xyz.shape
            new_xyz = torch.zeros(B, 1, C).to(device)
            grouped_xyz = xyz.view(B, 1, N, C)
            if points is not None:
                new_points = torch.cat([grouped_xyz, points.view(B, 1, N, -1)], dim=-1)
            else:
                new_points = grouped_xyz
            return new_xyz, new_points
        
        def farthest_point_sample(self, xyz, npoint):
            """Farthest point sampling"""
            device = xyz.device
            B, N, C = xyz.shape
            centroids = torch.zeros(B, npoint, dtype=torch.long).to(device)
            distance = torch.ones(B, N).to(device) * 1e10
            farthest = torch.randint(0, N, (B,), dtype=torch.long).to(device)
            batch_indices = torch.arange(B, dtype=torch.long).to(device)
            
            for i in range(npoint):
                centroids[:, i] = farthest
                centroid = xyz[batch_indices, farthest, :].view(B, 1, 3)
                dist = torch.sum((xyz - centroid) ** 2, -1)
                mask = dist < distance
                distance[mask] = dist[mask]
                farthest = torch.max(distance, -1)[1]
            
            return centroids
        
        def query_ball_point(self, radius, nsample, xyz, new_xyz):
            """Ball query"""
            device = xyz.device
            B, N, C = xyz.shape
            _, S, _ = new_xyz.shape
            group_idx = torch.arange(N, dtype=torch.long).to(device).view(1, 1, N).repeat([B, S, 1])
            sqrdists = self.square_distance(new_xyz, xyz)
            group_idx[sqrdists > radius ** 2] = N
            group_idx = group_idx.sort(dim=-1)[0][:, :, :nsample]
            group_first = group_idx[:, :, 0].view(B, S, 1).repeat([1, 1, nsample])
            mask = group_idx == N
            group_idx[mask] = group_first[mask]
            return group_idx
        
        def square_distance(self, src, dst):
            """Calculate squared distance"""
            B, N, _ = src.shape
            _, M, _ = dst.shape
            dist = -2 * torch.matmul(src, dst.permute(0, 2, 1))
            dist += torch.sum(src ** 2, -1).view(B, N, 1)
            dist += torch.sum(dst ** 2, -1).view(B, 1, M)
            return dist
        
        def index_points(self, points, idx):
            """Index points"""
            device = points.device
            B = points.shape[0]
            view_shape = list(idx.shape)
            view_shape[1:] = [1] * (len(view_shape) - 1)
            repeat_shape = list(idx.shape)
            repeat_shape[0] = 1
            batch_indices = torch.arange(B, dtype=torch.long).to(device).view(view_shape).repeat(repeat_shape)
            new_points = points[batch_indices, idx, :]
            return new_points
    
    print("PointNet++ Set Abstraction layer implemented")
else:
    print("Skipping PointNet++ implementation - PyTorch not available")

if TORCH_AVAILABLE:
    class PointNetPlusPlusCSectionDetector(nn.Module):
        """PointNet++ model for C-section pile detection"""
        def __init__(self, num_classes=2):
            super(PointNetPlusPlusCSectionDetector, self).__init__()
            
            # Set abstraction layers
            self.sa1 = PointNetSetAbstraction(npoint=512, radius=0.2, nsample=32, in_channel=6, mlp=[64, 64, 128], group_all=False)
            self.sa2 = PointNetSetAbstraction(npoint=128, radius=0.4, nsample=64, in_channel=128 + 3, mlp=[128, 128, 256], group_all=False)
            self.sa3 = PointNetSetAbstraction(npoint=None, radius=None, nsample=None, in_channel=256 + 3, mlp=[256, 512, 1024], group_all=True)
            
            # Classification head
            self.fc1 = nn.Linear(1024, 512)
            self.bn1 = nn.BatchNorm1d(512)
            self.drop1 = nn.Dropout(0.4)
            self.fc2 = nn.Linear(512, 256)
            self.bn2 = nn.BatchNorm1d(256)
            self.drop2 = nn.Dropout(0.4)
            self.fc3 = nn.Linear(256, num_classes)
        
        def forward(self, xyz):
            """
            Forward pass
            Input: xyz [B, 3, N] - point coordinates
            Output: classification scores [B, num_classes]
            """
            B, _, _ = xyz.shape
            
            # Add normal vectors as features (simplified - using coordinate differences)
            if xyz.shape[1] == 3:
                # Create simple features from coordinates
                features = torch.cat([
                    xyz,
                    xyz - xyz.mean(dim=2, keepdim=True),  # Centered coordinates
                    torch.norm(xyz, dim=1, keepdim=True).repeat(1, 3, 1)  # Distance from origin
                ], dim=1)
            else:
                features = xyz
            
            # Set abstraction layers
            l1_xyz, l1_points = self.sa1(xyz, features)
            l2_xyz, l2_points = self.sa2(l1_xyz, l1_points)
            l3_xyz, l3_points = self.sa3(l2_xyz, l2_points)
            
            # Global feature
            x = l3_points.view(B, 1024)
            
            # Classification
            x = self.drop1(F.relu(self.bn1(self.fc1(x))))
            x = self.drop2(F.relu(self.bn2(self.fc2(x))))
            x = self.fc3(x)
            
            return F.log_softmax(x, dim=1)
    
    print("PointNet++ C-section detector model implemented")
else:
    print("Skipping PointNet++ model - PyTorch not available")

class PointCloudPatchDataset(Dataset):
    """Dataset for point cloud patches"""
    def __init__(self, patches, labels=None, num_points=1024):
        self.patches = patches
        self.labels = labels
        self.num_points = num_points
    
    def __len__(self):
        return len(self.patches)
    
    def __getitem__(self, idx):
        patch = self.patches[idx]
        
        # Normalize patch to unit sphere
        centroid = patch.mean(axis=0)
        patch = patch - centroid
        max_dist = np.max(np.linalg.norm(patch, axis=1))
        if max_dist > 0:
            patch = patch / max_dist
        
        # Resample to fixed number of points
        if len(patch) >= self.num_points:
            # Random sampling
            indices = np.random.choice(len(patch), self.num_points, replace=False)
            patch = patch[indices]
        else:
            # Upsample with repetition
            indices = np.random.choice(len(patch), self.num_points, replace=True)
            patch = patch[indices]
        
        patch = torch.FloatTensor(patch).transpose(0, 1)  # [3, N]
        
        if self.labels is not None:
            label = torch.LongTensor([self.labels[idx]])
            return patch, label
        else:
            return patch

def generate_point_cloud_patches(points, patch_size=2.0, overlap=0.5, min_points=50):
    """Generate overlapping patches from point cloud"""
    print(f"Generating patches with size {patch_size}m, overlap {overlap}")
    
    # Calculate grid bounds
    x_min, y_min = points[:, 0].min(), points[:, 1].min()
    x_max, y_max = points[:, 0].max(), points[:, 1].max()
    
    step_size = patch_size * (1 - overlap)
    
    patches = []
    patch_centers = []
    
    x = x_min
    while x < x_max:
        y = y_min
        while y < y_max:
            # Define patch bounds
            x_patch_min, x_patch_max = x, x + patch_size
            y_patch_min, y_patch_max = y, y + patch_size
            
            # Extract points in patch
            mask = ((points[:, 0] >= x_patch_min) & (points[:, 0] <= x_patch_max) &
                   (points[:, 1] >= y_patch_min) & (points[:, 1] <= y_patch_max))
            
            patch_points = points[mask]
            
            if len(patch_points) >= min_points:
                patches.append(patch_points)
                patch_centers.append([x + patch_size/2, y + patch_size/2])
            
            y += step_size
        x += step_size
    
    print(f"Generated {len(patches)} patches")
    return patches, np.array(patch_centers)

print("Patch generation utilities implemented")

class GeometricCSectionDetector:
    """Geometric-based C-section pile detection using point cloud analysis."""
    
    def __init__(self):
        # C-section pile characteristics
        self.min_height = 1.5  # Minimum pile height above ground
        self.max_height = 10.0  # Maximum reasonable pile height
        self.typical_width = 0.15  # Typical C-section width (~15cm)
        self.typical_depth = 0.08  # Typical C-section depth (~8cm)
        self.width_tolerance = 0.08  # ±8cm tolerance
        self.min_points_per_pile = 40  # Minimum points (C-sections have fewer points than I-sections)
        
    def detect_vertical_structures(self, points):
        """Detect vertical structures that could be C-section piles."""
        print("Detecting vertical structures...")
        
        # Filter by height
        z_min = points[:, 2].min()
        height_mask = (points[:, 2] - z_min) >= self.min_height
        elevated_points = points[height_mask]
        
        print(f"Points above {self.min_height}m: {len(elevated_points):,}")
        
        if len(elevated_points) < self.min_points_per_pile:
            return []
        
        # Use smaller clustering radius for C-sections (they're thinner)
        clustering = DBSCAN(eps=0.3, min_samples=self.min_points_per_pile)
        cluster_labels = clustering.fit_predict(elevated_points[:, :2])
        
        # Extract clusters
        unique_labels = set(cluster_labels)
        unique_labels.discard(-1)  # Remove noise
        
        vertical_structures = []
        for label in unique_labels:
            cluster_mask = cluster_labels == label
            cluster_points = elevated_points[cluster_mask]
            
            if len(cluster_points) >= self.min_points_per_pile:
                vertical_structures.append(cluster_points)
        
        print(f"Found {len(vertical_structures)} vertical structure candidates")
        return vertical_structures
    
    def analyze_c_section_characteristics(self, structure_points):
        """Analyze if structure has C-section characteristics."""
        # Calculate structure dimensions
        x_span = structure_points[:, 0].max() - structure_points[:, 0].min()
        y_span = structure_points[:, 1].max() - structure_points[:, 1].min()
        z_span = structure_points[:, 2].max() - structure_points[:, 2].min()
        
        # C-section characteristics:
        # 1. Significant height (vertical structure)
        # 2. Moderate width (C-section opening)
        # 3. Smaller depth (C-section profile)
        # 4. Asymmetric cross-section (C-shape)
        
        height_score = min(z_span / self.min_height, 1.0)
        
        # Check dimensions for C-section profile
        max_horizontal = max(x_span, y_span)
        min_horizontal = min(x_span, y_span)
        
        # C-sections are typically smaller than I-sections
        width_score = 0.0
        if (self.typical_width - self.width_tolerance) <= max_horizontal <= (self.typical_width + self.width_tolerance):
            width_score = 0.7
        
        # C-sections have moderate aspect ratio (not as elongated as I-sections)
        aspect_ratio = max_horizontal / (min_horizontal + 1e-6)
        aspect_score = 0.0
        if 1.5 <= aspect_ratio <= 4.0:  # C-sections less elongated than I-sections
            aspect_score = 0.8
        
        # Analyze point distribution for C-shape characteristics
        distribution_score = self.analyze_point_distribution(structure_points)
        
        # Combine scores (weight distribution more heavily for C-sections)
        confidence = (height_score * 0.3 + width_score * 0.3 + 
                     aspect_score * 0.2 + distribution_score * 0.2)
        
        return {
            'confidence': confidence,
            'height': z_span,
            'width': max_horizontal,
            'depth': min_horizontal,
            'aspect_ratio': aspect_ratio,
            'point_count': len(structure_points),
            'distribution_score': distribution_score
        }
    
    def analyze_point_distribution(self, structure_points):
        """Analyze point distribution to detect C-shape characteristics."""
        if len(structure_points) < 20:
            return 0.0
        
        # Project points to horizontal plane and analyze distribution
        xy_points = structure_points[:, :2]
        
        # Center the points
        centroid = xy_points.mean(axis=0)
        centered_points = xy_points - centroid
        
        # Use PCA to find principal directions
        pca = PCA(n_components=2)
        pca.fit(centered_points)
        
        # Transform to principal component space
        transformed_points = pca.transform(centered_points)
        
        # Analyze distribution along principal axes
        pc1_std = np.std(transformed_points[:, 0])
        pc2_std = np.std(transformed_points[:, 1])
        
        # C-sections should have asymmetric distribution
        asymmetry_ratio = max(pc1_std, pc2_std) / (min(pc1_std, pc2_std) + 1e-6)
        
        # Score based on asymmetry (C-sections are more asymmetric than cylinders)
        if asymmetry_ratio > 2.0:
            return 0.8
        elif asymmetry_ratio > 1.5:
            return 0.6
        else:
            return 0.3
    
    def extract_pile_center(self, structure_points):
        """Extract pile center coordinates."""
        # Use centroid of bottom 20% of points
        z_min = structure_points[:, 2].min()
        z_threshold = z_min + 0.2 * (structure_points[:, 2].max() - z_min)
        
        base_mask = structure_points[:, 2] <= z_threshold
        base_points = structure_points[base_mask]
        
        if len(base_points) > 0:
            center = base_points.mean(axis=0)
        else:
            center = structure_points.mean(axis=0)
        
        return center
    
    def detect_c_section_piles(self, points):
        """Main detection pipeline."""
        print("Starting C-section pile detection...")
        
        # Step 1: Find vertical structures
        vertical_structures = self.detect_vertical_structures(points)
        
        if not vertical_structures:
            print("No vertical structures found")
            return []
        
        # Step 2: Analyze each structure for C-section characteristics
        detections = []
        for i, structure in enumerate(vertical_structures):
            analysis = self.analyze_c_section_characteristics(structure)
            
            if analysis['confidence'] >= confidence_threshold:
                center = self.extract_pile_center(structure)
                
                detection = {
                    'x': float(center[0]),
                    'y': float(center[1]),
                    'z': float(center[2]),
                    'confidence': float(analysis['confidence']),
                    'width': float(analysis['width']),
                    'height': float(analysis['height']),
                    'depth': float(analysis['depth']),
                    'c_section_score': float(analysis['confidence']),
                    'point_count': int(analysis['point_count']),
                    'distribution_score': float(analysis['distribution_score']),
                    'detection_method': 'geometric'
                }
                
                detections.append(detection)
        
        print(f"Detected {len(detections)} C-section pile candidates")
        return detections

# Initialize detector
detector = GeometricCSectionDetector()
print("Geometric C-section detector initialized")

if TORCH_AVAILABLE:
    print("==> POINTNET++ PATCH-BASED DETECTION")
    
    # Generate patches from the point cloud
    patches, patch_centers = generate_point_cloud_patches(points, patch_size=3.0, overlap=0.3, min_points=100)
    
    if len(patches) > 0:
        # Initialize model (randomly - in practice you'd load a trained model)
        model = PointNetPlusPlusCSectionDetector(num_classes=2)
        model.to(device)
        model.eval()
        
        print(f"Model initialized on {device}")
        print(f"Processing {len(patches)} patches...")
        
        # Create dataset and dataloader
        dataset = PointCloudPatchDataset(patches, num_points=1024)
        dataloader = DataLoader(dataset, batch_size=8, shuffle=False)
        
        # Run inference
        pointnet_detections = []
        patch_scores = []
        
        with torch.no_grad():
            for i, batch_patches in enumerate(dataloader):
                batch_patches = batch_patches.to(device)
                
                # Forward pass
                outputs = model(batch_patches)
                probabilities = torch.exp(outputs)  # Convert log probabilities to probabilities
                
                # Get C-section scores (class 1)
                c_section_scores = probabilities[:, 1].cpu().numpy()
                patch_scores.extend(c_section_scores)
                
                if i % 50 == 0:
                    print(f"  Processed {i * 8}/{len(patches)} patches")
        
        patch_scores = np.array(patch_scores)
        
        # Filter patches with high C-section probability
        high_confidence_mask = patch_scores > 0.7  # Threshold for C-section detection
        
        print(f"\nPointNet++ Results:")
        print(f"  Total patches: {len(patches)}")
        print(f"  High confidence patches: {np.sum(high_confidence_mask)}")
        print(f"  Mean score: {np.mean(patch_scores):.3f}")
        print(f"  Max score: {np.max(patch_scores):.3f}")
        
        # Convert high-confidence patches to pile detections
        if np.sum(high_confidence_mask) > 0:
            high_conf_centers = patch_centers[high_confidence_mask]
            high_conf_scores = patch_scores[high_confidence_mask]
            
            for center, score in zip(high_conf_centers, high_conf_scores):
                # Estimate Z coordinate from nearby points
                nearby_mask = ((points[:, 0] - center[0])**2 + (points[:, 1] - center[1])**2) < 1.0
                if np.sum(nearby_mask) > 0:
                    z_coord = points[nearby_mask, 2].mean()
                else:
                    z_coord = points[:, 2].mean()
                
                detection = {
                    'x': float(center[0]),
                    'y': float(center[1]),
                    'z': float(z_coord),
                    'confidence': float(score),
                    'width': 0.15,  # Typical C-section width
                    'height': 2.0,  # Estimated height
                    'depth': 0.08,  # Typical C-section depth
                    'detection_method': 'pointnet++'
                }
                pointnet_detections.append(detection)
        
        print(f"  Final detections: {len(pointnet_detections)}")
        
        # Store for comparison
        pointnet_results = {
            'detections': pointnet_detections,
            'patch_scores': patch_scores,
            'patch_centers': patch_centers
        }
    else:
        print("No patches generated - insufficient point density")
        pointnet_results = {'detections': [], 'patch_scores': [], 'patch_centers': []}
else:
    print("Skipping PointNet++ detection - PyTorch not available")
    pointnet_results = {'detections': [], 'patch_scores': [], 'patch_centers': []}

# Run geometric detection
geometric_detections = detector.detect_c_section_piles(points)

print(f"\n==> DETECTION RESULTS COMPARISON")
print(f"\nGeometric Method:")
print(f"  Total detections: {len(geometric_detections)}")
print(f"  Confidence threshold: {confidence_threshold}")

if TORCH_AVAILABLE:
    print(f"\nPointNet++ Method:")
    print(f"  Total detections: {len(pointnet_results['detections'])}")
    print(f"  Patches analyzed: {len(pointnet_results['patch_scores'])}")
    if len(pointnet_results['patch_scores']) > 0:
        print(f"  Mean patch score: {np.mean(pointnet_results['patch_scores']):.3f}")

# Combine results for final output (prefer PointNet++ if available, otherwise geometric)
if TORCH_AVAILABLE and len(pointnet_results['detections']) > 0:
    final_detections = pointnet_results['detections']
    detection_method_used = 'pointnet++'
    print(f"\nUsing PointNet++ detections for final results")
elif len(geometric_detections) > 0:
    final_detections = geometric_detections
    detection_method_used = 'geometric'
    print(f"\nUsing geometric detections for final results")
else:
    final_detections = []
    detection_method_used = 'none'
    print(f"\nNo detections found with either method")

if final_detections:
    confidences = [d['confidence'] for d in final_detections]
    heights = [d.get('height', 2.0) for d in final_detections]
    widths = [d.get('width', 0.15) for d in final_detections]
    
    print(f"\nFinal Detection Statistics ({detection_method_used}):")
    print(f"  Count: {len(final_detections)}")
    print(f"  Mean confidence: {np.mean(confidences):.3f}")
    print(f"  Confidence range: [{np.min(confidences):.3f}, {np.max(confidences):.3f}]")
    print(f"  Height range: [{np.min(heights):.2f}, {np.max(heights):.2f}] m")
    print(f"  Width range: [{np.min(widths):.2f}, {np.max(widths):.2f}] m")
    
    # Display individual detections
    print(f"\nDetailed Results:")
    for i, det in enumerate(final_detections[:10]):  # Show first 10
        print(f"  Pile {i+1}: ({det['x']:.1f}, {det['y']:.1f}, {det['z']:.1f}) "
              f"conf={det['confidence']:.3f} method={det.get('detection_method', 'unknown')}")
    if len(final_detections) > 10:
        print(f"  ... and {len(final_detections) - 10} more")
else:
    print("\nNo C-section piles detected with either method")
    print("Consider:")
    print("  - Lowering confidence threshold")
    print("  - Adjusting patch size for PointNet++")
    print("  - Training PointNet++ model on labeled data")

if enable_visualization and final_detections:
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # Plot 1: Top view with detections
    ax1.scatter(points[:, 0], points[:, 1], c=points[:, 2], s=1, alpha=0.6, cmap='viridis')
    
    # Overlay detections (use squares for C-sections to distinguish from I-sections)
    for det in final_detections:
        square = plt.Rectangle((det['x'] - det['width']/2, det['y'] - det['width']/2), 
                             det['width'], det['width'], 
                             fill=False, color='orange', linewidth=2)
        ax1.add_patch(square)
        ax1.text(det['x'], det['y'], f"{det['confidence']:.2f}", 
                ha='center', va='center', color='orange', fontweight='bold')
    
    ax1.set_xlabel('X (m)')
    ax1.set_ylabel('Y (m)')
    ax1.set_title(f'C-Section Pile Detection - Top View\n{len(final_detections)} detections')
    ax1.axis('equal')
    ax1.grid(True, alpha=0.3)
    
    # Plot 2: Side view
    ax2.scatter(points[:, 0], points[:, 2], c='blue', s=1, alpha=0.6)
    
    # Overlay detections
    for det in final_detections:
        ax2.scatter(det['x'], det['z'], c='orange', s=100, marker='s', linewidth=3)
        ax2.text(det['x'], det['z'] + 0.5, f"{det['confidence']:.2f}", 
                ha='center', va='bottom', color='orange', fontweight='bold')
    
    ax2.set_xlabel('X (m)')
    ax2.set_ylabel('Z (m)')
    ax2.set_title('C-Section Pile Detection - Side View')
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    if save_results:
        plt.savefig(output_path / f'c_section_detection_{ground_method}.png', dpi=300, bbox_inches='tight')
        print(f"Saved visualization: {output_path / f'c_section_detection_{ground_method}.png'}")
    
    plt.show()
else:
    print("No detections to visualize")

if save_results:
    print("\nSAVING RESULTS")
    
    # Save detections CSV
    if final_detections:
        df = pd.DataFrame(final_detections)
        csv_file = output_path / f'c_section_detections_{ground_method}.csv'
        df.to_csv(csv_file, index=False)
        print(f"Saved detections: {csv_file}")
    
    # Save metrics
    metrics = {
        'timestamp': datetime.now().isoformat(),
        'site_name': site_name,
        'ground_method': ground_method,
        'detection_method_used': detection_method_used,
        'pytorch_available': TORCH_AVAILABLE,
        'pile_type': 'c_section',
        'confidence_threshold': confidence_threshold,
        'total_detections': len(final_detections),
        'input_points': int(len(points)),
        'data_source': data_source,
        'use_aligned_data': use_aligned_data,
        'input_file': str(input_file),
        'detection_statistics': {
            'mean_confidence': float(np.mean([d['confidence'] for d in final_detections])) if final_detections else 0.0,
            'mean_height': float(np.mean([d.get('height', 2.0) for d in final_detections])) if final_detections else 0.0,
            'mean_width': float(np.mean([d.get('width', 0.15) for d in final_detections])) if final_detections else 0.0,
            'mean_distribution_score': float(np.mean([d.get('distribution_score', 0.0) for d in final_detections])) if final_detections else 0.0
        }
    }
    
    metrics_file = output_path / f'c_section_metrics_{ground_method}.json'
    with open(metrics_file, 'w') as f:
        json.dump(metrics, f, indent=2)
    print(f"Saved metrics: {metrics_file}")
    
    print(f"\nAll results saved to: {output_path}")

# Summary
print("\n" + "="*60)
print("C-SECTION PILE DETECTION SUMMARY")
print("="*60)
print(f"Site: {site_name}")
print(f"Ground method: {ground_method}")
print(f"Detection method: {detection_method_used}")
print(f"PyTorch available: {TORCH_AVAILABLE}")
print(f"Input points: {len(points):,}")
print(f"Geometric detections: {len(geometric_detections)}")
if TORCH_AVAILABLE:
    print(f"PointNet++ detections: {len(pointnet_results['detections'])}")
    print(f"Patches analyzed: {len(pointnet_results['patch_scores'])}")
print(f"Final detections: {len(final_detections)}")
print(f"Success rate: {'GOOD' if len(final_detections) > 0 else 'NO DETECTIONS'}")
print("="*60)
print("\nExploration Summary:")
print("- Implemented PointNet++ architecture for C-section detection")
print("- Created patch-based inference pipeline")
print("- Compared geometric vs deep learning approaches")
print("- Ready for model training with labeled data")
print("\nNext steps:")
print("- Collect labeled training data")
print("- Train PointNet++ model")
print("- Fine-tune hyperparameters")
print("- Integrate with alignment pipeline")