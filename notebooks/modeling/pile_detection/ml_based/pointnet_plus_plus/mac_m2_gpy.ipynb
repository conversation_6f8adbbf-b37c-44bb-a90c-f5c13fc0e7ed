{"cells": [{"cell_type": "code", "execution_count": 1, "id": "725df4e3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: pip in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (25.1.1)\n", "Collecting tensorflow-macos\n", "  Downloading tensorflow_macos-2.16.2-cp311-cp311-macosx_12_0_arm64.whl.metadata (3.3 kB)\n", "Collecting tensorflow==2.16.2 (from tensorflow-macos)\n", "  Downloading tensorflow-2.16.2-cp311-cp311-macosx_12_0_arm64.whl.metadata (4.1 kB)\n", "Requirement already satisfied: absl-py>=1.0.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from tensorflow==2.16.2->tensorflow-macos) (2.3.1)\n", "Requirement already satisfied: astunparse>=1.6.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from tensorflow==2.16.2->tensorflow-macos) (1.6.3)\n", "Requirement already satisfied: flatbuffers>=23.5.26 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from tensorflow==2.16.2->tensorflow-macos) (25.2.10)\n", "Requirement already satisfied: gast!=0.5.0,!=0.5.1,!=0.5.2,>=0.2.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from tensorflow==2.16.2->tensorflow-macos) (0.6.0)\n", "Requirement already satisfied: google-pasta>=0.1.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from tensorflow==2.16.2->tensorflow-macos) (0.2.0)\n", "Requirement already satisfied: h5py>=3.10.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from tensorflow==2.16.2->tensorflow-macos) (3.14.0)\n", "Requirement already satisfied: libclang>=13.0.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from tensorflow==2.16.2->tensorflow-macos) (18.1.1)\n", "Collecting ml-dtypes~=0.3.1 (from tensorflow==2.16.2->tensorflow-macos)\n", "  Downloading ml_dtypes-0.3.2-cp311-cp311-macosx_10_9_universal2.whl.metadata (20 kB)\n", "Requirement already satisfied: opt-einsum>=2.3.2 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from tensorflow==2.16.2->tensorflow-macos) (3.4.0)\n", "Requirement already satisfied: packaging in /Users/<USER>/.local/lib/python3.11/site-packages (from tensorflow==2.16.2->tensorflow-macos) (25.0)\n", "Requirement already satisfied: protobuf!=4.21.0,!=4.21.1,!=4.21.2,!=4.21.3,!=4.21.4,!=4.21.5,<5.0.0dev,>=3.20.3 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from tensorflow==2.16.2->tensorflow-macos) (3.20.3)\n", "Requirement already satisfied: requests<3,>=2.21.0 in /Users/<USER>/.local/lib/python3.11/site-packages (from tensorflow==2.16.2->tensorflow-macos) (2.32.4)\n", "Requirement already satisfied: setuptools in /Users/<USER>/.local/lib/python3.11/site-packages (from tensorflow==2.16.2->tensorflow-macos) (80.9.0)\n", "Requirement already satisfied: six>=1.12.0 in /Users/<USER>/.local/lib/python3.11/site-packages (from tensorflow==2.16.2->tensorflow-macos) (1.17.0)\n", "Requirement already satisfied: termcolor>=1.1.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from tensorflow==2.16.2->tensorflow-macos) (3.1.0)\n", "Requirement already satisfied: typing-extensions>=3.6.6 in /Users/<USER>/.local/lib/python3.11/site-packages (from tensorflow==2.16.2->tensorflow-macos) (4.14.0)\n", "Requirement already satisfied: wrapt>=1.11.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from tensorflow==2.16.2->tensorflow-macos) (1.17.2)\n", "Requirement already satisfied: grpcio<2.0,>=1.24.3 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from tensorflow==2.16.2->tensorflow-macos) (1.73.1)\n", "Collecting tensorboard<2.17,>=2.16 (from tensorflow==2.16.2->tensorflow-macos)\n", "  Downloading tensorboard-2.16.2-py3-none-any.whl.metadata (1.6 kB)\n", "Requirement already satisfied: keras>=3.0.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from tensorflow==2.16.2->tensorflow-macos) (3.10.0)\n", "Requirement already satisfied: tensorflow-io-gcs-filesystem>=0.23.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from tensorflow==2.16.2->tensorflow-macos) (0.37.1)\n", "Requirement already satisfied: numpy<2.0.0,>=1.23.5 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from tensorflow==2.16.2->tensorflow-macos) (1.26.4)\n", "Requirement already satisfied: charset_normalizer<4,>=2 in /Users/<USER>/.local/lib/python3.11/site-packages (from requests<3,>=2.21.0->tensorflow==2.16.2->tensorflow-macos) (3.4.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /Users/<USER>/.local/lib/python3.11/site-packages (from requests<3,>=2.21.0->tensorflow==2.16.2->tensorflow-macos) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /Users/<USER>/.local/lib/python3.11/site-packages (from requests<3,>=2.21.0->tensorflow==2.16.2->tensorflow-macos) (2.5.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in /Users/<USER>/.local/lib/python3.11/site-packages (from requests<3,>=2.21.0->tensorflow==2.16.2->tensorflow-macos) (2025.6.15)\n", "Requirement already satisfied: markdown>=2.6.8 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from tensorboard<2.17,>=2.16->tensorflow==2.16.2->tensorflow-macos) (3.8.2)\n", "Requirement already satisfied: tensorboard-data-server<0.8.0,>=0.7.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from tensorboard<2.17,>=2.16->tensorflow==2.16.2->tensorflow-macos) (0.7.2)\n", "Requirement already satisfied: werkzeug>=1.0.1 in /Users/<USER>/.local/lib/python3.11/site-packages (from tensorboard<2.17,>=2.16->tensorflow==2.16.2->tensorflow-macos) (3.1.3)\n", "Requirement already satisfied: wheel<1.0,>=0.23.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from astunparse>=1.6.0->tensorflow==2.16.2->tensorflow-macos) (0.45.1)\n", "Requirement already satisfied: rich in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from keras>=3.0.0->tensorflow==2.16.2->tensorflow-macos) (14.0.0)\n", "Requirement already satisfied: namex in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from keras>=3.0.0->tensorflow==2.16.2->tensorflow-macos) (0.1.0)\n", "Requirement already satisfied: optree in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from keras>=3.0.0->tensorflow==2.16.2->tensorflow-macos) (0.16.0)\n", "Requirement already satisfied: MarkupSafe>=2.1.1 in /Users/<USER>/.local/lib/python3.11/site-packages (from werkzeug>=1.0.1->tensorboard<2.17,>=2.16->tensorflow==2.16.2->tensorflow-macos) (3.0.2)\n", "Requirement already satisfied: markdown-it-py>=2.2.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from rich->keras>=3.0.0->tensorflow==2.16.2->tensorflow-macos) (3.0.0)\n", "Requirement already satisfied: pygments<3.0.0,>=2.13.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from rich->keras>=3.0.0->tensorflow==2.16.2->tensorflow-macos) (2.19.2)\n", "Requirement already satisfied: mdurl~=0.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from markdown-it-py>=2.2.0->rich->keras>=3.0.0->tensorflow==2.16.2->tensorflow-macos) (0.1.2)\n", "Downloading tensorflow_macos-2.16.2-cp311-cp311-macosx_12_0_arm64.whl (2.1 kB)\n", "Downloading tensorflow-2.16.2-cp311-cp311-macosx_12_0_arm64.whl (227.0 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m227.0/227.0 MB\u001b[0m \u001b[31m13.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m00:01\u001b[0m00:01\u001b[0m\n", "\u001b[?25hDownloading ml_dtypes-0.3.2-cp311-cp311-macosx_10_9_universal2.whl (389 kB)\n", "Downloading tensorboard-2.16.2-py3-none-any.whl (5.5 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m5.5/5.5 MB\u001b[0m \u001b[31m12.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0ma \u001b[36m0:00:01\u001b[0m\n", "\u001b[?25hInstalling collected packages: ml-dtypes, tensorboard, tensorflow, tensorflow-macos\n", "\u001b[2K  Attempting uninstall: ml-dtypes\n", "\u001b[2K    Found existing installation: ml_dtypes 0.5.1\n", "\u001b[2K    Uninstalling ml_dtypes-0.5.1:\n", "\u001b[2K      Successfully uninstalled ml_dtypes-0.5.1\n", "\u001b[2K  Attempting uninstall: tensorboard\n", "\u001b[2K    Found existing installation: tensorboard 2.19.0\n", "\u001b[2K    Uninstalling tensorboard-2.19.0:\n", "\u001b[2K      Successfully uninstalled tensorboard-2.19.0\n", "\u001b[2K  Attempting uninstall: tensorflow90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1/4\u001b[0m [tensorboard]\n", "\u001b[2K    Found existing installation: tensorflow 2.19.0━━━━━━━━━━━━\u001b[0m \u001b[32m1/4\u001b[0m [tensorboard]\n", "\u001b[2K    Uninstalling tensorflow-2.19.0:0m╺\u001b[0m\u001b[90m━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2/4\u001b[0m [tensorflow]\n", "\u001b[2K      Successfully uninstalled tensorflow-2.19.0━━━━━━━━━━━━━━\u001b[0m \u001b[32m2/4\u001b[0m [tensorflow]\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m4/4\u001b[0m [tensorflow-macos][tensorflow]\n", "\u001b[1A\u001b[2KSuccessfully installed ml-dtypes-0.3.2 tensorboard-2.16.2 tensorflow-2.16.2 tensorflow-macos-2.16.2\n", "Collecting tensorflow-metal\n", "  Downloading tensorflow_metal-1.2.0-cp311-cp311-macosx_12_0_arm64.whl.metadata (1.3 kB)\n", "Requirement already satisfied: wheel~=0.35 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from tensorflow-metal) (0.45.1)\n", "Requirement already satisfied: six>=1.15.0 in /Users/<USER>/.local/lib/python3.11/site-packages (from tensorflow-metal) (1.17.0)\n", "Downloading tensorflow_metal-1.2.0-cp311-cp311-macosx_12_0_arm64.whl (1.4 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.4/1.4 MB\u001b[0m \u001b[31m2.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0ma \u001b[36m0:00:01\u001b[0m\n", "\u001b[?25hInstalling collected packages: tensorflow-metal\n", "Successfully installed tensorflow-metal-1.2.0\n"]}], "source": ["!pip install --upgrade pip\n", "!pip install tensorflow-macos\n", "!pip install tensorflow-metal\n"]}, {"cell_type": "code", "execution_count": 3, "id": "759f63e3", "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "`np.complex_` was removed in the NumPy 2.0 release. Use `np.complex128` instead.", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mAttributeError\u001b[39m                            <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[3]\u001b[39m\u001b[32m, line 1\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m1\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01<PERSON><PERSON><PERSON><PERSON>\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mtf\u001b[39;00m\n\u001b[32m      2\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[33mTF version:\u001b[39m\u001b[33m\"\u001b[39m, tf.__version__)\n\u001b[32m      3\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[33mNumPy version:\u001b[39m\u001b[33m\"\u001b[39m, tf.experimental.numpy.__version__)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/tensorflow/__init__.py:45\u001b[39m\n\u001b[32m     42\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpython\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m tf2 \u001b[38;5;28;01mas\u001b[39;00m _tf2\n\u001b[32m     43\u001b[39m _tf2.enable()\n\u001b[32m---> \u001b[39m\u001b[32m45\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m<PERSON><PERSON>low\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01m_api\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mv2\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m __internal__\n\u001b[32m     46\u001b[39m \u001b[38;5;28;01mf<PERSON>\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01m_api\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mv2\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m __operators__\n\u001b[32m     47\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mtensorflow\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01m_api\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mv2\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m audio\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/tensorflow/_api/v2/__internal__/__init__.py:8\u001b[39m\n\u001b[32m      3\u001b[39m \u001b[33;03m\"\"\"Public API for tf._api.v2.__internal__ namespace\u001b[39;00m\n\u001b[32m      4\u001b[39m \u001b[33;03m\"\"\"\u001b[39;00m\n\u001b[32m      6\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01msys\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m_sys\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m8\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mtensorflow\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01m_api\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mv2\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01m__internal__\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m autograph\n\u001b[32m      9\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mtensorflow\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01m_api\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mv2\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01m__internal__\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m decorator\n\u001b[32m     10\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mtensorflow\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01m_api\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mv2\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01m__internal__\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m dispatch\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/tensorflow/_api/v2/__internal__/autograph/__init__.py:8\u001b[39m\n\u001b[32m      3\u001b[39m \u001b[33;03m\"\"\"Public API for tf._api.v2.__internal__.autograph namespace\u001b[39;00m\n\u001b[32m      4\u001b[39m \u001b[33;03m\"\"\"\u001b[39;00m\n\u001b[32m      6\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01msys\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m_sys\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m8\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mtensorflow\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpython\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mautograph\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mcore\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mag_ctx\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m control_status_ctx \u001b[38;5;66;03m# line: 34\u001b[39;00m\n\u001b[32m      9\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mtensorflow\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpython\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mautograph\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mimpl\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mapi\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m tf_convert \u001b[38;5;66;03m# line: 493\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/tensorflow/python/autograph/core/ag_ctx.py:21\u001b[39m\n\u001b[32m     18\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01minspect\u001b[39;00m\n\u001b[32m     19\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mthreading\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m21\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpython\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mautograph\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mutils\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m ag_logging\n\u001b[32m     22\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpython\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mutil\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mtf_export\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m tf_export\n\u001b[32m     25\u001b[39m stacks = threading.local()\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/tensorflow/python/autograph/utils/__init__.py:17\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[38;5;66;03m# Copyright 2016 The TensorFlow Authors. All Rights Reserved.\u001b[39;00m\n\u001b[32m      2\u001b[39m \u001b[38;5;66;03m#\u001b[39;00m\n\u001b[32m      3\u001b[39m \u001b[38;5;66;03m# Licensed under the Apache License, Version 2.0 (the \"License\");\u001b[39;00m\n\u001b[32m   (...)\u001b[39m\u001b[32m     13\u001b[39m \u001b[38;5;66;03m# limitations under the License.\u001b[39;00m\n\u001b[32m     14\u001b[39m \u001b[38;5;66;03m# ==============================================================================\u001b[39;00m\n\u001b[32m     15\u001b[39m \u001b[33;03m\"\"\"Utility module that contains APIs usable in the generated code.\"\"\"\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m17\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mtensorflow\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpython\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mautograph\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mutils\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mcontext_managers\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m control_dependency_on_returns\n\u001b[32m     18\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mtensorflow\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpython\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mautograph\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mutils\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mmisc\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m alias_tensors\n\u001b[32m     19\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mtensorflow\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpython\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mautograph\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mutils\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mtensor_list\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m dynamic_list_append\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/tensorflow/python/autograph/utils/context_managers.py:20\u001b[39m\n\u001b[32m     17\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mcontextlib\u001b[39;00m\n\u001b[32m     19\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m<PERSON>orflow\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpython\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mframework\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m ops\n\u001b[32m---> \u001b[39m\u001b[32m20\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mtensorflow\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpyt<PERSON>\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mops\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m tensor_array_ops\n\u001b[32m     23\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mcontrol_dependency_on_returns\u001b[39m(return_value):\n\u001b[32m     24\u001b[39m \u001b[38;5;250m  \u001b[39m\u001b[33;03m\"\"\"Create a TF control dependency on the return values of a function.\u001b[39;00m\n\u001b[32m     25\u001b[39m \n\u001b[32m     26\u001b[39m \u001b[33;03m  If the function had no return value, a no-op context is returned.\u001b[39;00m\n\u001b[32m   (...)\u001b[39m\u001b[32m     32\u001b[39m \u001b[33;03m    A context manager.\u001b[39;00m\n\u001b[32m     33\u001b[39m \u001b[33;03m  \"\"\"\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/tensorflow/python/ops/tensor_array_ops.py:36\u001b[39m\n\u001b[32m     34\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpython\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mframework\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m type_spec\n\u001b[32m     35\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpython\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mframework\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m type_spec_registry\n\u001b[32m---> \u001b[39m\u001b[32m36\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpython\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mops\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m array_ops\n\u001b[32m     37\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mtensorflow\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpython\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mops\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m array_ops_stack\n\u001b[32m     38\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mtensorflow\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpython\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mops\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m control_flow_util\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/tensorflow/python/ops/array_ops.py:22\u001b[39m\n\u001b[32m     19\u001b[39m \u001b[38;5;28;01mi<PERSON>rt\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mnumpy\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mnp\u001b[39;00m\n\u001b[32m     21\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m<PERSON><PERSON>low\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mcore\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mconfig\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m flags\n\u001b[32m---> \u001b[39m\u001b[32m22\u001b[39m \u001b[38;5;28;01mf<PERSON>\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01<PERSON><PERSON><PERSON><PERSON>\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01m<PERSON><PERSON>or\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m api \u001b[38;5;28;01mas\u001b[39;00m d_api\n\u001b[32m     23\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mtensorflow\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpython\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01meager\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m context\n\u001b[32m     24\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mtensorflow\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpython\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01meager\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m record\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/tensorflow/dtensor/python/api.py:21\u001b[39m\n\u001b[32m     18\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mthreading\u001b[39;00m\n\u001b[32m     19\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mtyping\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m Any, Callable, Optional, Sequence\n\u001b[32m---> \u001b[39m\u001b[32m21\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m<PERSON><PERSON>low\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mdtensor\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpython\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m dtensor_device\n\u001b[32m     22\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mdtensor\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpython\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m gen_dtensor_ops\n\u001b[32m     23\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mtensorflow\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mdtensor\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpython\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m layout \u001b[38;5;28;01mas\u001b[39;00m layout_lib\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/tensorflow/dtensor/python/dtensor_device.py:33\u001b[39m\n\u001b[32m     31\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpython\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mframework\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m dtypes\n\u001b[32m     32\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m<PERSON>orflow\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpython\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mframework\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m ops\n\u001b[32m---> \u001b[39m\u001b[32m33\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpython\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mframework\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m sparse_tensor\n\u001b[32m     34\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mtensorflow\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpython\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mframework\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m tensor_util\n\u001b[32m     35\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mtensorflow\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpython\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mutil\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m _pywrap_utils\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/tensorflow/python/framework/sparse_tensor.py:28\u001b[39m\n\u001b[32m     26\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpython\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mframework\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m dtypes\n\u001b[32m     27\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpython\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mframework\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m ops\n\u001b[32m---> \u001b[39m\u001b[32m28\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpyt<PERSON>\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mframework\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m override_binary_operator\n\u001b[32m     29\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mtensorflow\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpython\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mframework\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m tensor\n\u001b[32m     30\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mtensorflow\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpython\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mframework\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m tensor_shape\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/tensorflow/python/framework/override_binary_operator.py:24\u001b[39m\n\u001b[32m     22\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01<PERSON><PERSON><PERSON><PERSON>\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpython\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mframework\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m tensor_shape\n\u001b[32m     23\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpython\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mops\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m gen_math_ops\n\u001b[32m---> \u001b[39m\u001b[32m24\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01<PERSON><PERSON><PERSON><PERSON>\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpython\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mops\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mnumpy_ops\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m np_dtypes\n\u001b[32m     25\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mtensorflow\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpython\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mutil\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m nest\n\u001b[32m     26\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mtensorflow\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpython\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mutil\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m traceback_utils\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/tensorflow/python/ops/numpy_ops/np_dtypes.py:30\u001b[39m\n\u001b[32m     26\u001b[39m bool_ = np.bool_\n\u001b[32m     27\u001b[39m tf_export.tf_export(\u001b[33m'\u001b[39m\u001b[33mexperimental.numpy.bool_\u001b[39m\u001b[33m'\u001b[39m, v1=[]).export_constant(\n\u001b[32m     28\u001b[39m     \u001b[34m__name__\u001b[39m, \u001b[33m'\u001b[39m\u001b[33mbool_\u001b[39m\u001b[33m'\u001b[39m\n\u001b[32m     29\u001b[39m )\n\u001b[32m---> \u001b[39m\u001b[32m30\u001b[39m complex_ = \u001b[43mnp\u001b[49m\u001b[43m.\u001b[49m\u001b[43mcomplex_\u001b[49m\n\u001b[32m     31\u001b[39m tf_export.tf_export(\u001b[33m'\u001b[39m\u001b[33mexperimental.numpy.complex_\u001b[39m\u001b[33m'\u001b[39m, v1=[]).export_constant(\n\u001b[32m     32\u001b[39m     \u001b[34m__name__\u001b[39m, \u001b[33m'\u001b[39m\u001b[33mcomplex_\u001b[39m\u001b[33m'\u001b[39m\n\u001b[32m     33\u001b[39m )\n\u001b[32m     34\u001b[39m complex128 = np.complex128\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/numpy/__init__.py:400\u001b[39m, in \u001b[36m__getattr__\u001b[39m\u001b[34m(attr)\u001b[39m\n\u001b[32m    397\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mAttributeError\u001b[39;00m(__former_attrs__[attr], name=\u001b[38;5;28;01mNone\u001b[39;00m)\n\u001b[32m    399\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m attr \u001b[38;5;129;01min\u001b[39;00m __expired_attributes__:\n\u001b[32m--> \u001b[39m\u001b[32m400\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mAttributeError\u001b[39;00m(\n\u001b[32m    401\u001b[39m         \u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33m`np.\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mattr\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m` was removed in the NumPy 2.0 release. \u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    402\u001b[39m         \u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[38;5;132;01m{\u001b[39;00m__expired_attributes__[attr]\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m,\n\u001b[32m    403\u001b[39m         name=\u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[32m    404\u001b[39m     )\n\u001b[32m    406\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m attr == \u001b[33m\"\u001b[39m\u001b[33mchararray\u001b[39m\u001b[33m\"\u001b[39m:\n\u001b[32m    407\u001b[39m     warnings.warn(\n\u001b[32m    408\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33m`np.chararray` is deprecated and will be removed from \u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    409\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33mthe main namespace in the future. Use an array with a string \u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    410\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33mor bytes dtype instead.\u001b[39m\u001b[33m\"\u001b[39m, \u001b[38;5;167;01mDeprecationWarning\u001b[39;00m, stacklevel=\u001b[32m2\u001b[39m)\n", "\u001b[31mAttributeError\u001b[39m: `np.complex_` was removed in the NumPy 2.0 release. Use `np.complex128` instead."]}], "source": ["import tensorflow as tf\n", "print(\"TF version:\", tf.__version__)\n", "print(\"NumPy version:\", tf.experimental.numpy.__version__)\n", "print(\"GPUs:\", tf.config.list_physical_devices('GPU'))\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}